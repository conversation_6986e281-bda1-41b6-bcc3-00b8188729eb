// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model File {
  id                       Int              @id @default(autoincrement())
  title                    String?
  name                     String?
  url                      String
  type                     FileType
  createdAt                DateTime         @default(now())
  updatedAt                DateTime         @updatedAt
  article1                 Article?         @relation("ArticleCoverFile")
  article2                 Article?         @relation("ArticleVideoFile")
  User                     User?            @relation("UserAvatarImg")
  articleCategoryIconImage ArticleCategory? @relation("ArticleCategoryIconImage")
  AppointmentVerification AppointmentVerification[] @relation("AppointmentVerificationFile")
  threadsIconImage Threads[] @relation("ThreadsIconFile")
}

enum FileType {
  IMAGE
  VIDEO
}

model ArticleCategory {
  id                    Int                    @id @default(autoincrement())
  name                  String
  description           String?
  type                  ArticleCategoryType    @default(UNGROUPED)
  order                 Int                    @default(0)
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
  articleUpdatedAt      DateTime?
  articles              Article[]
  iconFileId       Int?                   @unique
  iconFile         File?                  @relation("ArticleCategoryIconImage", fields: [iconFileId], references: [id])
  userSubscribedCategories UserSubscribedCategory[]
}

enum ArticleCategoryType {
  UNGROUPED
  AEC
  AEC_OPEN_DAY
  AEA
}


// 修改后的 Article 模型，表示文章
model Article {
  id          Int              @id @default(autoincrement())
  title       String // 文章标题
  summary     String?
  content     String?
  tags        String[]         @default([]) // 文章标签选择是否可以在主页显示
  type        ArticleType      @default(NORMAL) // 文章类型 普通文章或者视频文章
  status      ArticleStatus    @default(DRAFT)
  likes Int @default(0)
  comments Int @default(0)
  views Int @default(0)
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  categoryId  Int?
  category    ArticleCategory? @relation(fields: [categoryId], references: [id])

  articleCoverFileId Int?  @unique
  articleCoverFile   File? @relation("ArticleCoverFile", fields: [articleCoverFileId], references: [id]) // 文章封面图片
  articleVideoFileId Int?  @unique
  articleVideoFile   File? @relation("ArticleVideoFile", fields: [articleVideoFileId], references: [id]) // 视频文章

  @@index([title])
  UserFavoriteArticle UserFavoriteArticle[]
  UserLikedArticle UserLikedArticle[]
  UserViewHistory UserViewHistory[]
}

enum ArticleType {
  NORMAL
  VIDEO
}

enum ArticleStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}



model UserFavoriteArticle {
  id Int @id @default(autoincrement())
  userId String
  articleId Int
  article Article @relation(fields: [articleId], references: [id])
  user User @relation(fields: [userId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model UserLikedArticle {
  id Int @id @default(autoincrement())
  userId String
  articleId Int
  article Article @relation(fields: [articleId], references: [id])
  user User @relation(fields: [userId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}


model UserSubscribedCategory {
  id Int @id @default(autoincrement())
  userId String
  categoryId Int
  category ArticleCategory @relation(fields: [categoryId], references: [id])
  user User @relation(fields: [userId], references: [id])
  createdAt DateTime @default(now())
  lastReadAt DateTime?
}


model UserViewHistory{
  id Int @id @default(autoincrement())
  userId String
  postId Int
  articleId Int
  article Article @relation(fields: [articleId], references: [id])
  user User @relation(fields: [userId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deletedAt DateTime?
}

// 帖子主题
model Threads{
  id Int @id @default(autoincrement())
  name                  String
  description           String?
  iconFileId Int?
  iconFile File? @relation("ThreadsIconFile", fields: [iconFileId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  Posts Posts[]
}

// 用户发帖
model Posts{
  id Int @id @default(autoincrement())
  threadId Int
  thread Threads @relation(fields: [threadId], references: [id])
  userId String
  user User @relation(fields: [userId], references: [id])
  content String
  likes Int @default(0)
  comments Int @default(0)
  views Int @default(0)
  parentId Int?
  parent Posts? @relation("PostsParent", fields: [parentId], references: [id])
  status PostsStatus @default(DRAFT)
  children Posts[] @relation("PostsParent")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deletedAt DateTime?
  PostLog PostLog[]
}

enum PostsStatus {
  DRAFT // 草稿
  PENDING // 待审核
  APPROVED // 审核通过,正常显示
  REJECTED // 审核不通过,不显示
  DELETED // 删除,不显示
}

model PostLog{
  id Int @id @default(autoincrement())
  postId Int
  post Posts @relation(fields: [postId], references: [id])
  userId String
  user User @relation(fields: [userId], references: [id])
  system Boolean @default(false)
  status PostsStatus @default(PENDING)
  message String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}


// Account 模型，表示用户账户信息
model Account {
  id                       String  @id @default(cuid())
  userId                   String
  type                     String
  provider                 String
  providerAccountId        String
  refresh_token            String? // @db.Text
  access_token             String? // @db.Text
  expires_at               Int?
  token_type               String?
  scope                    String?
  id_token                 String? // @db.Text
  session_state            String?
  user                     User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  refresh_token_expires_in Int?

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

// Session 模型，表示用户会话信息
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// UserGroup 枚举，表示用户组
enum UserGroup {
  USER
  SALES
  TRAINER
  MKT
  ADMIN
  SUPERADMIN
  SYSADMIN
}

// User 模型，表示用户信息
model User {
  id                      String                    @id @default(cuid())
  username                String                    @unique
  name                    String?
  email                   String?                   @unique
  password                String?
  emailVerified           DateTime?                 @map("email_verified")
  avatarImgId             Int?                      @unique
  avatarImg               File?                     @relation("UserAvatarImg", fields: [avatarImgId], references: [id])
  usergroups              UserGroup[]               @default([USER])
  accounts                Account[]
  sessions                Session[]
  importAt                DateTime?
  createdAt               DateTime                  @default(now())
  updatedAt               DateTime                  @updatedAt
  AppointmentOperationLog AppointmentOperationLog[]
  additionalInfo          UserAdditionalInfo?       @relation("UserInfo")
  @@map("users")
  UserFavoriteArticle UserFavoriteArticle[]
  UserLikedArticle UserLikedArticle[]
  UserSubscribedCategory UserSubscribedCategory[]
  Posts Posts[]
  PostLog PostLog[]
  UserViewHistory UserViewHistory[]
}

model UserAdditionalInfo {
  id         Int      @id @default(autoincrement())
  userId     String   @unique
  phone      String?
  department String?
  region     String?
  employeeId String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  user       User?    @relation("UserInfo", fields: [userId], references: [id])
}

// VerificationToken 模型，表示验证令牌
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Location 模型，表示被预约项目所在的位置/城市
model Location {
  id                  Int                  @id @default(autoincrement())
  name                String
  description         String?
  adminEmails         String[]
  notifyPolicy        Int   @default(0)
  autoConfirmPolicy   AutoConfirmPolicy    @default(ALWAYS)
  virtual             Boolean              @default(false)
  visiable            Boolean              @default(true)
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  appointmentProjects AppointmentProject[] @relation("AppointmentProjectLocation")
  stockItems          StockItem[]          @relation("locationStockItems")
}

// notifyAbout : 创建,确认,取消 目前仅针对这三种
// 000 ~ 111

enum AutoConfirmPolicy {
  NEVER
  PERIOD_NOT_MEET_WEEKEND_AND_HOLIDAY
  ALWAYS
}


model AppointmentProject {
  id          Int                            @id @default(autoincrement())
  name        String
  description String? // 项目描述  （项目简介等）
  details     String? // 预约说明 （到访须知+预约规则）
  extraInfo   String? // 额外信息 （如 培训地点等需要自定义的信息）
  type        AppointmentProjectType // 表示预约类型（参观、培训、实验）
  locationId  Int // 外键，关联 Location 模型
  optionId    Int?
  location    Location                       @relation("AppointmentProjectLocation", fields: [locationId], references: [id])
  option      AppointmentProjectExtraOption? @relation("AppointmentProjectExtraOption", fields: [optionId], references: [id])
  createdAt   DateTime                       @default(now())
  updatedAt   DateTime                       @updatedAt

  timeRules    AppointmentProjectTimeRule[] @relation("AppointmentProjectTimeRules")
  appointments Appointment[]                @relation("appointmentAppointmentProject")

  restrictedRules AppointmentProjectRestrictionRule[] @relation("AppointmentProjectRestrictions")
  groupRules      AppointmentProjectRestrictionRule[] @relation("AppointmentProjectsGroup")
}

// 预约类型（参观、培训、实验）
enum AppointmentProjectType {
  PROCEDURE // 术式
  TOUR // 参观
  TRAINING // 培训
  LIVE // 直播  
}

model AppointmentProjectExtraOption {
  id                 Int                    @id @default(autoincrement())
  name               String
  description        String?
  type               AppointmentProjectType
  appointmentProject AppointmentProject[]   @relation("AppointmentProjectExtraOption")
  createdAt          DateTime               @default(now())
  updatedAt          DateTime               @updatedAt
}

// make several projects as a group , the maxCapacity if for the group
model AppointmentProjectRestrictionRule {
  id                 Int                  @id @default(autoincrement())
  projects           AppointmentProject[] @relation("AppointmentProjectsGroup")
  type               RestrictionType
  maxCapacity        Int? // this is only for a period of time, eg. 2025-03-01 09:00 to 2025-03-01 11:00
  restrictedProjects AppointmentProject[] @relation("AppointmentProjectRestrictions")
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
}

model AppointmentProjectTimeRule {
  id        Int                  @id @default(autoincrement())
  type      TimeRuleType
  scope     TimeRuleScope
  projects  AppointmentProject[] @relation("AppointmentProjectTimeRules")
  start     DateTime?
  end       DateTime?
  locked    Boolean              @default(false)
  order     Int                  @default(0)
  createdAt DateTime             @default(now())
}

model TimeSlot {
  id           Int           @id @default(autoincrement())
  startTime    DateTime
  endTime      DateTime
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  appointments Appointment[] @relation("appointmenTimeSlot")
}

model TimeSolotRestrictionCache {
  id                Int                           @id @default(autoincrement())
  type              TimeSolotRestrictionCacheType
  startTime         DateTime
  endTime           DateTime
  projects          Int[]
  appointments      Int[]
  maxCapacity       Int?
  remainingCapacity Int?
  createdAt         DateTime                      @default(now())
  updatedAt         DateTime                      @updatedAt

  @@index([startTime, endTime])
}

enum TimeSolotRestrictionCacheType {
  CAPACITY
  RESTRICTION
}

// Appointment 模型，表示预约
model Appointment {
  id                      Int                       @id @default(autoincrement())
  timeSlotId              Int
  appointmentProjectId    Int
  remark                  String? // 备注字段
  customers               Customer[]                @relation("CustomerAppointment")
  status                  AppointmentStatus         @default(CREATED) // 和下面AppointmentOperationLog 最新的一条status同步
  customersCount          Int                       @default(0) // 对于无需提供客户信息的项目，这里记录预约人数
  createdAt               DateTime                  @default(now())
  updatedAt               DateTime                  @updatedAt
  AppointmentOperationLog AppointmentOperationLog[] @relation("AppointmentOperationLog")
  timeSlot                TimeSlot                  @relation("appointmenTimeSlot", fields: [timeSlotId], references: [id])
  appointmentProject      AppointmentProject        @relation("appointmentAppointmentProject", fields: [appointmentProjectId], references: [id])

  internalCustomers InternalCustomer[] @relation("InternalAppointmentCustomer")
}

model AppointmentOperationLog {
  id              Int                      @id @default(autoincrement())
  userId          String? // 操作用户ID，记录谁执行了这个操作
  appointmentId   Int
  message         String? // 填写的信息，如取消原因
  status          AppointmentStatus        @default(CREATED) // 默认为已创建状态
  appointment     Appointment              @relation("AppointmentOperationLog", fields: [appointmentId], references: [id])
  createdAt       DateTime                 @default(now())
  createdBy       User?                    @relation(fields: [userId], references: [id])
  createdBySystem Boolean                  @default(false) // 是否是系统创建的, 自动审核，自动完成
  verification    AppointmentVerification? // 关联的核销记录
}

// 预约核销表
model AppointmentVerification {
  id                          Int      @id @default(autoincrement())
  operationLogId              Int      @unique // 关联的操作日志ID
  fileId                      Int?     @unique
  appointmentVerificationFile File?    @relation("AppointmentVerificationFile", fields: [fileId], references: [id])
  createdAt                   DateTime @default(now())
  updatedAt                   DateTime @updatedAt

  // 关联
  operationLog AppointmentOperationLog @relation(fields: [operationLogId], references: [id])
}

// Customer 模型，表示预约时填写的客户信息
model Customer {
  id            Int         @id @default(autoincrement())
  name          String
  hospitalId    Int
  contactInfo   String?
  // organizationId Int
  appointmentId Int
  appointments  Appointment @relation("CustomerAppointment", fields: [appointmentId], references: [id])
  hospital      Hospital    @relation("CustomerHospital", fields: [hospitalId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())
}

model InternalCustomer {
  id            Int         @id @default(autoincrement())
  name          String
  department    String
  appointmentId Int
  appointments  Appointment @relation("InternalAppointmentCustomer", fields: [appointmentId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())
}

// StockItem 模型，表示库存项
model StockItem {
  id              Int                @id @default(autoincrement())
  sku             String             @unique // Unique Stock Keeping Unit
  name            String
  unit            StockItemUnit      @default(ITEM)
  description     String?
  currentQuantity Int // Tracks the current stock quantity
  locationId      Int // 外键，关联 Location 模型
  location        Location           @relation("locationStockItems", fields: [locationId], references: [id])
  createdAt       DateTime           @default(now())
  updatedAt       DateTime           @updatedAt
  transactions    StockTransaction[]
}

model StockTransaction {
  id          Int             @id @default(autoincrement())
  stockItemId Int // Foreign key to StockItem
  stockItem   StockItem       @relation(fields: [stockItemId], references: [id])
  type        TransactionType // Enum to differentiate between "addition" and "consumption"
  quantity    Int // The amount added or consumed
  createdAt   DateTime        @default(now())
}

model Hospital {
  id        Int        @id @default(autoincrement())
  sfid      Int        @unique
  name      String
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  Customer  Customer[] @relation("CustomerHospital")
}

enum RestrictionType {
  MAX_CAPACITY
  RESTRICTED_TO
}

enum AppointmentStatus {
  CREATED
  CONFIRMED
  REJECTED
  CANCELLED
  CHECKED
}

enum TimeRuleType {
  INCLUDE
  EXCLUDE
}

enum TimeRuleScope {
  DAILY // Applies to weekdays
  WEEKEND // Applies to weekends
  HOLIDAY // Applies to holidays
  DATE_RANGE // Applies to a specific date range
  SPECIFIC_DATE // Applies to a specific date
}

enum StockItemUnit {
  // 基础单位（Basic Units）
  ITEM // 件
  PIECE // 个
  STICK // 支
  HANDLE // 把
  UNIT // 单位

  // 包装单位（Package Units）
  BOX // 盒
  PACKAGE // 包
  BAG // 袋
  CARTON // 箱
  CASE // 盒
  BUNDLE // 捆
  PACK // 包
  BARREL // 桶

  // 长度单位（Length Units）
  ROLL // 卷
  METER // 米
  CENTIMETER // 厘米
  STRIP // 条
  FEET // 英尺
  INCH // 英寸

  // 面积单位（Area Units）
  SQUARE_METER // 平方米
  SQUARE_CENTIMETER // 平方厘米

  // 特殊单位（Special Units）
  SET // 套
  SHEET // 片
  BOTTLE // 瓶
  TUBE // 管
  AMPOULE // 安瓿
  VIAL // 西林瓶
  SYRINGE // 注射器
  PAIR // 对/副
  KIT // 套装

  // 重量单位（Weight Units）
  GRAM // 克
  KILOGRAM // 千克
  MILLIGRAM // 毫克
  POUND // 磅
  OUNCE // 盎司

  // 体积单位（Volume Units）
  MILLILITER // 毫升
  LITER // 升
  CUBIC_CENTIMETER // 立方厘米
  GALLON // 加仑

  // 浓度单位（Concentration Units）
  PERCENT // 百分比
  PPM // 百万分之

  // 时间单位（Time Units）
  HOUR // 小时
  DAY // 天
  MONTH // 月

  // 能量单位（Energy Units）
  JOULE // 焦耳
  CALORIE // 卡路里

  // 压力单位（Pressure Units）
  MMHG // 毫米汞柱
  KPA // 千帕

  // 医疗特殊单位（Medical Special Units）
  DOSE // 剂量
  UNIT_IU // 国际单位
  TEST // 测试次数
  PROCEDURE // 操作次数
  TREATMENT // 治疗次数

  // 辐射单位（Radiation Units）
  GRAY // 戈瑞
  SIEVERT // 希沃特

  // 电气单位（Electrical Units）
  VOLT // 伏特
  WATT // 瓦特
  AMPERE // 安培

  // 温度单位（Temperature Units）
  CELSIUS // 摄氏度
  FAHRENHEIT // 华氏度
}

enum TransactionType {
  ADDITION
  CONSUMPTION
}
