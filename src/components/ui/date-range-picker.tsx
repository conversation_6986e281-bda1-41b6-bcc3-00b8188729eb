"use client"

import * as React from "react"
import { CalendarIcon } from "@radix-ui/react-icons"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import type { DateRange } from "react-day-picker"

import { cn } from "~/lib/utils"
import { Button } from "~/components/ui/button"
import { Calendar } from "~/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select"

interface DateRangePickerProps {
  value?: DateRange
  onChange?: (range: DateRange | undefined) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function DateRangePicker({
  value,
  onChange,
  placeholder = "选择日期范围",
  className,
  disabled = false,
}: DateRangePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false)

  // Quick select options
  const quickSelects = [
    {
      label: "今天",
      getValue: () => {
        const today = new Date()
        return { from: today, to: today }
      },
    },
    {
      label: "昨天",
      getValue: () => {
        const yesterday = new Date()
        yesterday.setDate(yesterday.getDate() - 1)
        return { from: yesterday, to: yesterday }
      },
    },
    {
      label: "最近7天",
      getValue: () => {
        const today = new Date()
        const weekAgo = new Date()
        weekAgo.setDate(weekAgo.getDate() - 6)
        return { from: weekAgo, to: today }
      },
    },
    {
      label: "最近30天",
      getValue: () => {
        const today = new Date()
        const monthAgo = new Date()
        monthAgo.setDate(monthAgo.getDate() - 29)
        return { from: monthAgo, to: today }
      },
    },
    {
      label: "本月",
      getValue: () => {
        const today = new Date()
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1)
        const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0)
        return { from: firstDay, to: lastDay }
      },
    },
    {
      label: "上月",
      getValue: () => {
        const today = new Date()
        const firstDay = new Date(today.getFullYear(), today.getMonth() - 1, 1)
        const lastDay = new Date(today.getFullYear(), today.getMonth(), 0)
        return { from: firstDay, to: lastDay }
      },
    },
  ]

  const handleQuickSelect = (getValue: () => DateRange) => {
    const range = getValue()
    onChange?.(range)
    setIsOpen(false)
  }

  const formatDateRange = (range: DateRange | undefined) => {
    if (!range?.from) {
      return placeholder
    }

    if (!range.to) {
      return format(range.from, "yyyy年MM月dd日", { locale: zhCN })
    }

    if (range.from.getTime() === range.to.getTime()) {
      return format(range.from, "yyyy年MM月dd日", { locale: zhCN })
    }

    return `${format(range.from, "yyyy年MM月dd日", { locale: zhCN })} - ${format(range.to, "yyyy年MM月dd日", { locale: zhCN })}`
  }

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            className={cn(
              "w-[300px] justify-start text-left font-normal",
              !value && "text-muted-foreground"
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {formatDateRange(value)}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="flex">
            {/* Quick select sidebar */}
            <div className="border-r p-3">
              <div className="text-sm font-medium mb-2">快速选择</div>
              <div className="space-y-1">
                {quickSelects.map((option) => (
                  <Button
                    key={option.label}
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start text-sm"
                    onClick={() => handleQuickSelect(option.getValue)}
                  >
                    {option.label}
                  </Button>
                ))}
              </div>
            </div>
            
            {/* Calendar */}
            <div className="p-3">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={value?.from}
                selected={value}
                onSelect={onChange}
                numberOfMonths={2}
                locale={zhCN}
              />
            </div>
          </div>
          
          {/* Action buttons */}
          <div className="border-t p-3 flex justify-end space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                onChange?.(undefined)
                setIsOpen(false)
              }}
            >
              清除
            </Button>
            <Button
              size="sm"
              onClick={() => setIsOpen(false)}
              disabled={!value?.from}
            >
              确定
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
} 