"use client";
import { forwardRef } from "react";
import BaseModal, { BaseModalRef, BaseModalProps } from "./base-modal";
import AEButton from "../../app/common/ae-button";

export interface ConfirmationModalProps extends Omit<BaseModalProps, "footer"> {
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}

const ConfirmationModal = forwardRef<BaseModalRef, ConfirmationModalProps>(
  (
    {
      confirmText = "是，继续访问",
      cancelText = "否，退出访问",
      onConfirm,
      onCancel,
      ...baseProps
    },
    ref,
  ) => {
    const handleConfirm = () => {
      baseProps.onClose?.();
      onConfirm?.();
    };

    const handleCancel = () => {
      baseProps.onClose?.();
      onCancel?.();
    };

    const footer = (
      <div className="mt-6 flex w-full justify-between border-t border-[#efefef] pt-6">
        <AEButton className="w-[130px] text-center px-0" variant="gray" onClick={handleCancel}>
          {cancelText}
        </AEButton>
        <AEButton className="w-[130px] text-center px-0 outline-none" onClick={handleConfirm}>
          {confirmText}
        </AEButton>
      </div>
    );

    return <BaseModal ref={ref} {...baseProps} footer={footer} />;
  },
);

ConfirmationModal.displayName = "ConfirmationModal";

export default ConfirmationModal;
