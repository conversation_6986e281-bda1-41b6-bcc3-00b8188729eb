"use client";
import {
  forwardRef,
  useImperativeHandle,
  useEffect,
  useState,
  ReactNode,
} from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogOverlay,
  DialogPortal,
} from "../../ui/dialog";
import { BookMarkIcon } from "../../app/common/icons";

export interface BaseModalProps {
  title?: ReactNode;
  content?: ReactNode;
  footer?: ReactNode;
  initiallyOpened?: boolean;
  onOpen?: () => void;
  onClose?: () => void;
  preventOutsideClose?: boolean;
  customTitle?: ReactNode;
}

export interface BaseModalRef {
  openModal: () => void;
  closeModal: () => void;
}

const BaseModal = forwardRef<BaseModalRef, BaseModalProps>(
  (
    {
      title,
      content,
      footer,
      initiallyOpened = false,
      onOpen,
      onClose,
      preventOutsideClose = true,
      customTitle,
    },
    ref,
  ) => {
    const [opened, setOpened] = useState(initiallyOpened);

    const open = () => setOpened(true);
    const close = () => {
      setOpened(false);
      onClose?.();
    };

    useEffect(() => {
      console.log("BaseModal initiallyOpened:", initiallyOpened);
      if (initiallyOpened) {
        open();
        onOpen?.();
      }
    }, [initiallyOpened, onOpen]);

    useImperativeHandle(ref, () => ({
      openModal: () => {
        open();
        onOpen?.();
      },
      closeModal: close,
    }));

    const handleOutsideInteraction = (e: Event) => {
      if (preventOutsideClose) {
        e.preventDefault();
      }
    };

    return (
      <Dialog
        open={opened}
        onOpenChange={(value) => {
          console.log("Dialog onOpenChange:", value);
          if (!preventOutsideClose || !value) {
            setOpened(value);
          }
        }}
      >
        <DialogPortal>
          <DialogOverlay className="z-[21] bg-black/5" />
          <DialogContent
            className="w-[90%] !rounded-[20px] p-0 sm:min-w-[400px]"
            onInteractOutside={handleOutsideInteraction}
            onEscapeKeyDown={handleOutsideInteraction}
            onPointerDownOutside={handleOutsideInteraction}
          >
            <div className="px-[38px] py-[40px]">
              <DialogHeader className="p-0">
                {customTitle ?? (
                  <div className="flex w-full items-center justify-between border-b border-[#efefef] pb-5">
                    <DialogTitle className="text-[18px] font-bold leading-[18px] text-[#0F40F5]">
                      {title}
                    </DialogTitle>
                    <BookMarkIcon className="h-5 w-5" />
                  </div>
                )}
              </DialogHeader>
              <div className="mt-4">
                <div className="text-[#6C6C6C]">{content}</div>
              </div>
              {footer}
            </div>
          </DialogContent>
        </DialogPortal>
      </Dialog>
    );
  },
);

BaseModal.displayName = "BaseModal";

export default BaseModal;
