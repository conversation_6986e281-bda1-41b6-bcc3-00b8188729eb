"use client";
import { forwardRef } from "react";
import BaseModal, { BaseModalRef, BaseModalProps } from "./base-modal";
import AEButton from "../../app/common/ae-button";

export interface AnnouncementModalProps extends Omit<BaseModalProps, "footer"> {
  confirmText?: string;
  onConfirm?: () => void;
  storageKey?: string;
}

const AnnouncementModal = forwardRef<BaseModalRef, AnnouncementModalProps>(
  (
    {
      confirmText = "我已知晓",
      onConfirm,
      storageKey = "announcement",
      ...baseProps
    },
    ref,
  ) => {
    const handleConfirm = () => {
      if (storageKey && typeof window !== 'undefined') {
        try {
          sessionStorage.setItem(`hasSeenModal_${storageKey}`, "true");
        } catch (e) {
          console.error("Failed to set sessionStorage:", e);
        }
      }
      baseProps.onClose?.();
      onConfirm?.();
    };

    const footer = (
      <div className="mt-4 flex w-full flex-col items-center border-t border-[#efefef]">
        <div className="my-6 font-bold text-[#6C6C6C]">
          谢谢您的配合与支持!
        </div>
        <AEButton className="outline-none focus:outline-none" onClick={handleConfirm}>
          {confirmText}
        </AEButton>
      </div>
    );

    return <BaseModal ref={ref} {...baseProps} footer={footer} />;
  },
);

AnnouncementModal.displayName = "AnnouncementModal";

export default AnnouncementModal;
