"use client";
import { forwardRef } from "react";
import BaseModal, { BaseModalRef, BaseModalProps } from "./base-modal";
import AEButton from "../../app/common/ae-button";
import {
  AeCheckIconOutline,
  ClassroomIcon,
  WarningTriangleIcon,
} from "../../app/common/icons";
import { useRouter } from "next/navigation";

export interface SuccessModalProps extends Omit<BaseModalProps, "footer" | "title" | "content"> {
  confirmText?: string;
  onConfirm?: () => void;
}

const SuccessModal = forwardRef<BaseModalRef, SuccessModalProps>(
  (
    {
      confirmText = "我已知晓",
      onConfirm,
      ...baseProps
    },
    ref,
  ) => {
    const router = useRouter();

    const handleConfirm = () => {
      baseProps.onClose?.();
      onConfirm?.();
    };

    const title = (
      <div className="flex w-full flex-col items-center border-b border-[#efefef] pb-6 pt-5">
        <AeCheckIconOutline className="mb-4 h-12 w-12" />
        <div className="text-[#0F40F5] text-xl font-bold">预约成功</div>
      </div>
    );

    const content = (
      <div className="mt-2 flex flex-col items-center">
        <WarningTriangleIcon className="mb-2 mt-4 h-8 w-8" />
        <div className="flex flex-col items-center text-[#6C6C6C]">
          <div>现场不要忘记签到哦！</div>
          <div className="font-semibold">否则会限制您的预约权限</div>
          <div className="font-semibold">可能影响您的下一次预约哦～</div>
        </div>

        <div className="mt-4">
          <span>（签到入口：</span>
          <span
            className="cursor-pointer border-b border-[#0F40F5] pb-[4px] text-[#0F40F5]"
            onClick={() => router.push("/profile")}
          >
            <ClassroomIcon className="mb-[2px] inline w-4" />
            <span>个人中心</span>
          </span>
          <span>）</span>
        </div>
      </div>
    );

    const footer = (
      <div className="mt-6 flex flex-col items-center w-full border-t border-[#efefef]">
        <div className="my-10 font-bold text-[#6C6C6C]">
          谢谢您的配合与支持!
        </div>
        <AEButton className="outline-none" onClick={handleConfirm}>
          {confirmText}
        </AEButton>
      </div>
    );

    return (
      <BaseModal
        ref={ref}
        {...baseProps}
        customTitle={title}
        content={content}
        footer={footer}
      />
    );
  },
);

SuccessModal.displayName = "SuccessModal";

export default SuccessModal;
