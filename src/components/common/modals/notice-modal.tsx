"use client";
import { forwardRef } from "react";
import BaseModal, { BaseModalRef, BaseModalProps } from "./base-modal";
import AEButton from "../../app/common/ae-button";

export interface NoticeModalProps extends Omit<BaseModalProps, "footer"> {
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}

const NoticeModal = forwardRef<BaseModalRef, NoticeModalProps>(
  (
    {
      confirmText = "我已知晓",
      cancelText = "返回上一层",
      onConfirm,
      onCancel,
      ...baseProps
    },
    ref,
  ) => {
    const handleConfirm = () => {
      baseProps.onClose?.();
      onConfirm?.();
    };

    const handleCancel = () => {
      baseProps.onClose?.();
      onCancel?.();
    };

    const footer = (
      <div className="mt-6 flex w-full justify-between border-t border-[#efefef] pt-6">
        <AEButton variant="gray" onClick={handleCancel}>
          {cancelText}
        </AEButton>
        <AEButton className="outline-none" onClick={handleConfirm}>
          {confirmText}
        </AEButton>
      </div>
    );

    return <BaseModal ref={ref} {...baseProps} footer={footer} />;
  },
);

NoticeModal.displayName = "NoticeModal";

export default NoticeModal;
