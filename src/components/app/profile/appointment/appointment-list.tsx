"use client";

import { useEffect, useRef, useState } from "react";
import ProfileTabs, { Status } from "~/components/app/profile/profile-tabs";
import ProfileAppointmentCard from "~/components/app/profile/appointment/appointment-card";
import { api } from "~/trpc/react";
import Loading from "~/components/app/common/loading";
import { useSession } from "next-auth/react";

import { AppointmentStatus } from "@prisma/client";

// 后端API返回的预约状态类型
// 现在控制器直接返回AppointmentStatus枚举值

// 项目类型
type ProjectType = "PROCEDURE" | "TOUR" | "TRAINING" | "LIVE";

// 现在直接使用API返回的数据格式，不需要额外定义类型

// 组件中使用的预约数据类型
export interface AppointmentData {
  id: number;
  projectName: string;
  projectType: ProjectType;
  status: AppointmentStatus;
  appointmentDate: string;
  startTime: Date;
  endTime: Date;
  remark: string;
  bookedBy: string;
  customerCount: number;
  locationName: string; // 添加地点信息
}

export function AppointmentList() {
  // 当前选中的状态标签
  const [currentStatus, setCurrentStatus] = useState<Status>(Status.All);
  const session = useSession();
  const utils = api.useUtils();

  // 创建ref用于观察最后一个元素
  const observerRef = useRef<IntersectionObserver | null>(null);
  const lastAppointmentRef = useRef<HTMLDivElement | null>(null);

  // 使用useInfiniteQuery替代useQuery
  const {
    data: infiniteData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
  } = api.basic.userAppointment.getUserAppointments.useInfiniteQuery(
    {
      limit: 10, // 每页10条
    },
    {
      getNextPageParam: (lastPage) => lastPage.nextCursor,
      refetchOnMount: true,
      refetchOnWindowFocus: true,
      staleTime: 0,
    }
  );

  // 将所有页面的数据合并为一个数组
  const apiAppointments = infiniteData?.pages.flatMap(page => page.items) ?? [];

  // 将API返回的预约数据映射到组件需要的格式
  const appointments = apiAppointments.map((appointment) => {
    // 从项目名称推断项目类型
    const projectType: ProjectType =
      appointment.projectName.includes("培训") ? "TRAINING" :
      appointment.projectName.includes("实验") || appointment.projectName.includes("术式") ? "PROCEDURE" :
      appointment.projectName.includes("直播") ? "LIVE" : "TOUR";

    // 控制器现在直接返回AppointmentStatus枚举值，不需要进行状态映射
    const status = appointment.status;

    // 返回转换后的预约数据
    return {
      id: appointment.id,
      projectName: appointment.projectName,
      projectType,
      status,
      appointmentDate: appointment.appointmentDate ?? "-",
      startTime: appointment.startTime,
      endTime: appointment.endTime,
      remark: appointment.remark ?? "",
      bookedBy: "", // 在组件中从session获取
      customerCount: appointment.customerCount ?? 0,
      locationName: appointment.locationName ?? "未知地点" // 添加地点信息
    } as AppointmentData;
  });

  // 设置IntersectionObserver
  useEffect(() => {
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        // 如果最后一个元素进入视口，且有下一页，则加载下一页
        if (entries[0]?.isIntersecting && hasNextPage && !isFetchingNextPage) {
          void fetchNextPage();
        }
      },
      { threshold: 0.5 }
    );

    // 观察最后一个元素
    if (lastAppointmentRef.current) {
      observerRef.current.observe(lastAppointmentRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [fetchNextPage, hasNextPage, isFetchingNextPage]);

  // 当用户切换标签时，重置查询状态
  useEffect(() => {
    void utils.basic.userAppointment.getUserAppointments.invalidate();
  }, [currentStatus, utils.basic.userAppointment.getUserAppointments]);

  // 初始化取消预约mutation
  const cancelAppointmentMutation =
    api.basic.userAppointment.cancelAppointment.useMutation({});

  // 处理取消预约操作
  const handleCancel = async (appointmentId: number, cancelReason?: string) => {
    try {
      // 调用取消预约的API
      const result = await cancelAppointmentMutation.mutateAsync({
        appointmentId,
        cancelReason, // 传递取消原因
      });
      // 使用invalidateQueries替代refetch
      await utils.basic.userAppointment.getUserAppointments.invalidate();
      return result.success;
    } catch (error) {
      console.error("取消预约失败", error);
      return false;
    }
  };

  // 处理标签切换
  const handleStatusChange = (status: Status) => {
    setCurrentStatus(status);
  };

  // 根据当前标签状态过滤预约列表
  const getFilteredAppointments = () => {
    if (!appointments) return [];

    if (currentStatus === Status.All) {
      return appointments;
    } else if (currentStatus === Status.InProgress) {
      // 进行中的预约：待审核(CREATED)和已确认/签到(CONFIRMED)
      return appointments.filter(app =>
        app.status === AppointmentStatus.CREATED || app.status === AppointmentStatus.CONFIRMED
      );
    } else if (currentStatus === Status.Completed) {
      // 已完成的预约：已完成(CHECKED)、已取消(CANCELLED)和未通过(REJECTED)
      return appointments.filter(app =>
        app.status === AppointmentStatus.CHECKED ||
        app.status === AppointmentStatus.CANCELLED ||
        app.status === AppointmentStatus.REJECTED
      );
    }

    return appointments;
  };

  if (isLoading) {
    return <Loading />;
  }

  // 获取过滤后的预约数据
  const filteredAppointments = getFilteredAppointments();

  return (
    <>
      <ProfileTabs onStatusChange={handleStatusChange} />
      <div className="space-y-4">
        {filteredAppointments.map((appointment, index) => (
          <div
            key={appointment.id}
            ref={index === filteredAppointments.length - 1 ? lastAppointmentRef : null}
          >
            <ProfileAppointmentCard
              appointmentId={appointment.id}
              name={appointment.projectName}
              status={appointment.status}
              projectType={appointment.projectType}
              appointmentDate={appointment.appointmentDate ?? "-"}
              startTime={appointment.startTime}
              endTime={appointment.endTime}
              bookedBy={session.data?.user?.name ?? ""}
              customerCount={appointment.customerCount}
              locationName={appointment.locationName}
              onCancel={(reason) => handleCancel(appointment.id, reason)}
              remark={appointment.remark}
            />
          </div>
        ))}

        {isFetchingNextPage && (
          <div className="py-4 text-center">
            <Loading />
          </div>
        )}

        {filteredAppointments.length === 0 && !isFetchingNextPage && (
          <div className="py-8 text-center text-gray-500">
            {currentStatus === Status.All
              ? "暂无预约记录"
              : currentStatus === Status.InProgress
                ? "暂无进行中的预约"
                : "暂无已完成的预约"}
          </div>
        )}
      </div>
    </>
  );
}

export default AppointmentList;
