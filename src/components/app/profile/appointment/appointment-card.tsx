import React, { useRef, useState } from "react";
import { differenceInDays, format, isSameDay as isSameDayFn } from "date-fns";
import AEButton from "../../common/ae-button";
import ModalCancel from "../common/modal-cancel";
import { useRouter } from "next/navigation";
import { Clock, XCircle, CircleCheck } from "lucide-react";
import { AppointmentStatus } from "@prisma/client";

interface ProfileAppointmentCardProps {
  name?: string;
  appointmentId: number;
  status?: AppointmentStatus;
  projectType?: "PROCEDURE" | "TOUR" | "TRAINING" | "LIVE";
  appointmentDate?: string;
  startTime?: Date;
  endTime?: Date;
  remark?: string;
  bookedBy?: string;
  customerCount?: number;
  locationName?: string; // 添加地点信息
  onCancel?: (reason?: string) => void;
}

// Helper function to format date to M.d format (e.g., 4.1)
const formatDate = (date: Date): string => {
  return format(date, "yyyy-MM-dd");
};

const formatTimeRange = (startTime: Date, endTime: Date): string => {
  if (isSameDay(startTime, endTime)) {
    return `${format(startTime, "yyyy-MM-dd HH:mm")} ~ ${format(endTime, "HH:mm")}`;
  }
  return `${format(startTime, "yyyy-MM-dd HH:mm")} ~ ${format(endTime, "yyyy-MM-dd HH:mm")}`;
};

// Helper function to check if two dates are on the same day
const isSameDay = (date1: Date, date2: Date): boolean => {
  return isSameDayFn(date1, date2);
};

const ProfileAppointmentCard: React.FC<ProfileAppointmentCardProps> = ({
  appointmentId,
  name = "项目预约名称",
  status = AppointmentStatus.CREATED,
  projectType = "TOUR",
  startTime = new Date(),
  endTime = new Date(),
  remark = "",
  bookedBy = "未知",
  customerCount = 0,
  locationName = "未知地点",
  onCancel: onConfirmCancel,
}) => {
  const modalRef = useRef<{ openModal: () => void }>(null);
  const [modalMode, setModalMode] = useState<"cancel" | "success">("cancel");
  const [loading, setLoading] = useState(false);
  const [requireReason, setRequireReason] = useState(false);
  const router = useRouter();

  // 检查当前时间是否已过预约时间段结束时间
  const isAfterAppointment = () => {
    const now = new Date();
    return now > startTime;
  };

  // 判断是否为实验或培训项目
  const isTrainingOrProcedure = () => {
    return projectType === "PROCEDURE" || projectType === "TRAINING";
  };

  // 根据预约状态和时间判断显示的图标和操作
  // 使用AppointmentStatus枚举构建状态配置
  const statusConfig = {
    [AppointmentStatus.CREATED]: {
      icon: (
        <Clock className="mb-[5px] mr-[5px] h-[4.5rem] w-[4.5rem] stroke-[2.4] text-[#CFDBF0]" />
      ),
      text: "待审核",
    },
    [AppointmentStatus.CONFIRMED]: {
      icon:
        isTrainingOrProcedure() && isAfterAppointment() ? (
          <div
            className="flex h-20 w-20 cursor-pointer items-center justify-center rounded-full bg-[#0F40F5] text-2xl font-semibold text-white"
            onClick={() =>
              router.push(`/profile/validation?appointmentId=${appointmentId}`)
            }
          >
            签到
          </div>
        ) : (
          <div className="flex h-20 w-20 flex-col items-center justify-center rounded-full bg-[#4CE2BA] text-white">
            <span className="text-lg font-medium">已确认</span>
          </div>
        ),
      text: "已确认",
    },
    [AppointmentStatus.REJECTED]: {
      icon: (
        <div className="flex h-20 w-20 flex-col items-center justify-center rounded-full bg-[#FF4D4F] text-white">
          <span className="text-lg font-medium">未通过</span>
        </div>
      ),
      text: "未通过",
    },
    [AppointmentStatus.CANCELLED]: {
      icon: (
        <div className="flex h-20 w-20 flex-col items-center justify-center rounded-full bg-zinc-400 text-white">
          <span className="text-lg font-medium">已取消</span>
        </div>
      ),
      text: "已取消",
    },
    [AppointmentStatus.CHECKED]: {
      icon: (
        <div className="flex h-20 w-20 flex-col items-center justify-center rounded-full bg-blue-500 text-white">
          <span className="text-lg font-medium">已完成</span>
        </div>
      ),
      text: "已完成",
    },
  };

  // 确保状态是有效的AppointmentStatus枚举值
  const config =
    statusConfig[status] || statusConfig[AppointmentStatus.CREATED];

  const handleCancel = () => {
    setModalMode("cancel");

    // 检查是否在预约日期的3天内需要填写取消原因
    const currentDate = new Date();
    const diff = Math.abs(differenceInDays(startTime, currentDate));

    // 如果在3天内取消预约，需要提供取消原因
    setRequireReason(diff <= 3);

    modalRef.current?.openModal();
  };

  const handleConfirmCancel = async (reason?: string) => {
    if (!onConfirmCancel) return;

    setLoading(true);
    try {
      // 传递预约ID和取消原因给父组件的onCancel回调
      await Promise.resolve(onConfirmCancel(reason));
      setModalMode("success");
      modalRef.current?.openModal();
    } catch (error) {
      console.error("Failed to cancel appointment:", error);
      // 这里可以添加错误处理，比如显示错误提示
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <div className={`rounded-lg border bg-white p-4 shadow-md`}>
        <div className={`ae-title mb-2 font-bold text-[#0F40F5]`}>{name}</div>
        <div className="flex w-full items-center justify-between gap-2">
          <div className={`flex-1 text-sm text-[#9A9A9A]`}>
            {/* <div className="mb-2 hidden">
              状态：
              <span
                className={`font-medium ${
                  status === AppointmentStatus.CHECKED
                    ? "text-green-600"
                    : status === AppointmentStatus.CANCELLED ||
                        status === AppointmentStatus.REJECTED
                      ? "text-red-600"
                      : status === AppointmentStatus.CONFIRMED
                        ? "text-blue-600"
                        : "text-yellow-600"
                }`}
              >
                {statusConfig[status]?.text || "待确认"}
              </span>
            </div> */}
            <div className="mb-2">预约人：{bookedBy}</div>
            <div className="mb-2">预约人数：{customerCount}</div>
            <div className="mb-2">
              预约时段：{formatTimeRange(startTime, endTime)}
            </div>
            <div className="mb-2">
              预约地点：{locationName}
            </div>

            {/* {remark && <div className="mb-2 hidden">备注：{remark}</div>} */}
            {onConfirmCancel &&
              status !== AppointmentStatus.CHECKED &&
              status !== AppointmentStatus.CANCELLED &&
              status !== AppointmentStatus.REJECTED &&
              !isAfterAppointment() && (
                <AEButton
                  size="xs"
                  className="px-3 py-1"
                  onClick={handleCancel}
                >
                  取消预约
                </AEButton>
              )}
          </div>
          <div className="w-22">{config.icon}</div>
        </div>
      </div>

      <ModalCancel
        ref={modalRef}
        mode={modalMode}
        onCancel={handleConfirmCancel}
        projectName={name}
        loading={loading}
        requireReason={requireReason}
      />
    </>
  );
};

export default ProfileAppointmentCard;
