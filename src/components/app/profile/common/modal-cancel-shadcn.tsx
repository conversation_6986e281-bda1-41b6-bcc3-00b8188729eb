"use client";
import { forwardRef, useImperativeHandle, useEffect, useState } from "react";
import AEButton from "../../common/ae-button";
import { AeCheckIconOutline, WarningIcon } from "../../common/icons";
import {
  Dialog,
  DialogContent,
  DialogPortal,
  DialogOverlay,
  DialogTitle,
} from "~/components/ui/dialog";
import { useDisclosure } from "~/hooks/use-disclosure";

interface ModalCancelProps {
  onCancel?: (reason?: string) => void;
  initiallyOpened?: boolean;
  onOpen?: () => void;
  mode?: "success" | "cancel";
  projectName?: string;
  loading?: boolean;
  requireReason?: boolean;
}

const ModalCancel = forwardRef<
  {
    openModal: () => void;
  },
  ModalCancelProps
>(
  (
    {
      onCancel,
      initiallyOpened = false,
      onOpen,
      mode = "success",
      projectName = "项目名称",
      loading = false,
      requireReason = false,
    },
    ref,
  ) => {
    const { opened, open, close } = useDisclosure(initiallyOpened);
    const [cancelReason, setCancelReason] = useState("");
    
    useEffect(() => {
      if (initiallyOpened) {
        open();
        onOpen?.();
      }
    }, [initiallyOpened, open, onOpen]);

    useImperativeHandle(ref, () => ({
      openModal: () => {
        open();
        onOpen?.();
      },
    }));

    const handleConfirm = () => {
      if (mode === "success") {
        close();
      } else {
        // 如果需要填写取消原因但未填写，直接返回不执行取消操作
        if (requireReason && !cancelReason.trim()) {
          return;
        }
        onCancel?.(cancelReason);
      }
    };

    return (
      <Dialog open={opened} onOpenChange={(isOpen) => {
        if (!isOpen) close();
      }}>
        <DialogPortal>
          <DialogOverlay className="bg-black/30" />
          <DialogTitle className="sr-only" />
          <DialogContent className="sm:max-w-md">
            {mode === "success" ? (
              <div className="mt-2 flex flex-col items-center">
                <AeCheckIconOutline className="mb-4 mt-4 h-14 w-14" />
                <div className="flex flex-col items-center text-lg font-bold text-[#0F40F5]">
                  <div>预约取消成功</div>
                </div>
              </div>
            ) : (
              <div className="mt-2 flex flex-col items-center">
                <WarningIcon className="mb-2 mt-4 h-14 w-14" />
                <div className="flex flex-col items-center font-bold text-[#FF662A]">
                  <div>确定要取消这个预约吗</div>
                </div>
                {requireReason && (
                  <div className="mt-4 w-full max-w-sm">
                    <div className="mb-2 text-sm font-normal text-[#6C6C6C]">请填写取消原因（必填）：</div>
                    <textarea
                      className="w-full rounded-md border border-[#efefef] p-2 text-sm"
                      rows={3}
                      placeholder="请填写取消原因"
                      value={cancelReason}
                      onChange={(e) => setCancelReason(e.target.value)}
                      required
                    />
                  </div>
                )}
              </div>
            )}

            <div className="my-4 flex w-full flex-col items-center border-t border-[#efefef]">
              <div className="my-8 font-bold text-[#6C6C6C]">{projectName}</div>
              <div className="flex gap-4">
                {mode === "cancel" && (
                  <AEButton
                    size="sm"
                    variant="gray"
                    onClick={close}
                    disabled={loading}
                  >
                    返回上一层
                  </AEButton>
                )}
                <AEButton
                  size="sm"
                  onClick={handleConfirm}
                  loading={loading}
                  disabled={loading}
                >
                  {mode === "success" ? "我已知晓" : "确认取消"}
                </AEButton>
              </div>
            </div>
          </DialogContent>
        </DialogPortal>
      </Dialog>
    );
  },
);

ModalCancel.displayName = "ModalCancel";

export default ModalCancel;
