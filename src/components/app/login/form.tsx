"use client";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { LockKeyhole, MailIcon } from "lucide-react";
import AEButton from "../common/ae-button";
import AETextField from "../common/form/ae-text-filed";
import { signIn } from "next-auth/react";
import { useToast } from "~/hooks/use-toast";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { generateReloadUrl } from "~/lib/url";
import Link from "next/link";


// 邮箱格式验证正则表达式
const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

const FormSchema = z.object({
  email: z.string().regex(emailRegex, {
    message: "请输入有效的邮箱地址",
  }),
  password: z.string(), // 取消密码的最小长度验证
});

interface LoginFormProps {
  searchParams?: Record<string, string | string[]>;
  onSubmit?: (data: { email: string; password: string }, callbackUrl: string) => Promise<void>;
}

function LoginForm({
  searchParams,
  onSubmit: externalOnSubmit,
}: LoginFormProps): JSX.Element {
  const router = useRouter();
  const { toast } = useToast();
  const [validationState, setValidationState] = useState({
    email: "none",
    password: "none",
  });


  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  // 监听表单字段值
  const email = watch("email");
  const password = watch("password");

  // 判断登录按钮是否禁用
  const isLoginButtonDisabled = !email || !password;

  const callbackUrl = React.useMemo<string>(() => {
    // 1. 直接从searchParams中获取callbackUrl (最常见的情况)
    if (typeof searchParams?.callbackUrl === "string") {
      return searchParams.callbackUrl;
    }

    // 2. 处理数组形式的callbackUrl参数
    if (Array.isArray(searchParams?.callbackUrl) && searchParams?.callbackUrl.length > 0) {
      return searchParams.callbackUrl[0];
    }

    // 3. 尝试从searchParams.value中解析callbackUrl
    try {
      const value = (searchParams?.value as string) ?? "";
      const params = JSON.parse(value);
      if (params.callbackUrl) {
        return params.callbackUrl;
      }
    } catch {
      // 解析失败，继续尝试其他方法
    }

    // 4. 检查是否有referer或来源页面信息
    if (typeof window !== "undefined" && document.referrer) {
      const referrerUrl = new URL(document.referrer);
      if (referrerUrl.origin === window.location.origin) {
        return referrerUrl.pathname + referrerUrl.search;
      }
    }

    // 5. 如果都没有，则返回个人中心路径而不是首页
    return "/profile";
  }, [searchParams]);

  // 表单提交处理
  const onSubmit = (data: { email: string; password: string }) => {
    // 直接执行登录操作
    void performLogin(data);
  };

  // 执行实际的登录操作
  const performLogin = async (data: { email: string; password: string }) => {
    // 如果提供了外部的onSubmit函数，则使用它
    if (externalOnSubmit) {
      await externalOnSubmit(data, callbackUrl);
      return;
    }

    // 默认的登录处理逻辑
    const result = await signIn("credentials", {
      redirect: false,
      username: data.email, // 使用邮箱作为用户名参数
      password: data.password,
      callbackUrl: callbackUrl, // 显式传递callbackUrl
    });

    if (result?.error) {
      toast({
        title: "登录失败",
        description: "请检查邮箱和密码是否正确",
      });
      setValidationState({
        email: "invalid",
        password: "invalid",
      });
    } else {
      toast({
        title: "登录成功",
        description: "欢迎回来！",
      });

      // 优先使用NextAuth返回的url
      if (result?.url) {
        router.push(result.url);
      }
      // 如果是默认个人中心且有历史记录，则返回上一页
      else if (callbackUrl === "/profile" && typeof window !== "undefined" && window.history.length > 1) {
        router.back();
      }
      // 否则使用自定义callbackUrl
      else {
        const url = generateReloadUrl(callbackUrl);
        router.push(url);
      }
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)} className="px-4">
        <div className="mb-4 flex items-end justify-between">
          <h1 className="ae_title">账号登录</h1>
          <p className="mt-2 text-gray-500">（仅内部人员可使用）</p>
        </div>
        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <AETextField
              containerClassName="mb-4"
              type="email"
              id="email"
              placeholder="请输入邮箱地址"
              prefix={<MailIcon className="h-4 w-4" />}
              validationState={
                validationState.email === "invalid"
                  ? "invalid"
                  : errors.email
                    ? "invalid"
                    : emailRegex.test(field.value)
                      ? "valid"
                      : "none"
              }
              {...field}
              onChange={(e) => {
                field.onChange(e);
                setValidationState((prevState) => ({
                  ...prevState,
                  email: emailRegex.test(e.target.value) ? "valid" : "none",
                }));
              }}
            />
          )}
        />
        <Controller
          name="password"
          control={control}
          render={({ field }) => (
            <AETextField
              type="password"
              id="password"
              placeholder="请输入密码"
              prefix={<LockKeyhole className="h-4 w-4" />}
              validationState={
                validationState.password === "invalid"
                  ? "invalid"
                  : errors.password
                    ? "invalid"
                    : "none"
              }
              {...field}
              onChange={(e) => {
                field.onChange(e);
                setValidationState((prevState) => ({
                  ...prevState,
                  password: e.target.value ? "valid" : "none",
                }));
              }}
            />
          )}
        />
        <div className="flex justify-center pt-[40px]">
          <AEButton
            className="h-[30px] w-[115px] p-0"
            type="submit"
            disabled={isLoginButtonDisabled}
          >
            登录
          </AEButton>
        </div>

        {/* AEA登录链接 */}
        <div className="mt-6 flex justify-center text-sm">
          <Link href="/register" className="text-blue-600 hover:underline">
            AEA登录
          </Link>
        </div>
      </form>

    </>
  );
}

export default LoginForm;
