"use client";

import Link from "next/link";
import React from "react";
import { Button } from "~/components/ui/button";
import { Separator } from "~/components/ui/separator";
import type { SubNavItemProps } from "~/constants/navs";

import { useLocationStore } from "~/hooks/useLocationStore";
import { cn } from "~/lib/utils";


interface AsideProps {
  activeId: number | string;
  title: string;
  createLink?: (raw: string) => string;
  navs: SubNavItemProps[];
  children?: React.ReactNode;
}

export default function AsideNav({ activeId, title, createLink, navs, children }: AsideProps) {
  return (
    <div className="w-full space-y-4 pb-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">{title}</h2>
      </div>
      <Separator />
      <div className="flex flex-col lg:flex-row w-full justify-between flex-wrap gap-4">
        <div>
          {
            <nav className={cn("flex flex-row space-x-2")}>
              {navs.map((item) => (
                <Link
                  className="no-underline"
                  key={item.href}
                  href={createLink ? createLink(item.href) : item.href}
                  passHref
                >
                  <Button
                    className={cn("font-normal",  {
                      "bg-sky-600 hover:bg-sky-600": activeId === item.id,
                    })}
                    asChild
                    variant={activeId === item.id ? "default" : "link"}
                  >
                    <span> {item.label}</span>
                  </Button>
                </Link>
              ))}
            </nav>
          }
        </div>
        <div className="w-full lg:hidden">
          <Separator />
        </div>
        <div>
          {children}
        </div>
      </div>
    </div>

  );
}
