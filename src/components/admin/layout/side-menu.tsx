"use client";

import Link from "next/link";
import Image from "next/image";
import React from "react";
import {NavItem} from "~/components/admin/layout/nav-item";
import { useNavItemsWithActive } from "~/hooks/useNavItemsWithActive";


export function AdminSideMenu() {
  const navItems = useNavItemsWithActive();
  return (
    <div className="hidden border-r bg-sidebar  md:block">
      <div className="flex h-full max-h-screen flex-col gap-2">
        <div className="flex h-14 items-center border-b px-4 lg:h-[60px] lg:px-6">
          <Link href="/" className="flex items-center gap-2 font-semibold">
       
            <div className="flex aspect-square size-9 items-center justify-center rounded-lg text-sidebar-primary-foreground bg-blue-500">
            <Image src="/alcon-logo.svg" width={8} height={8} alt="Alcon Inc" className="h-8 w-8" />
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-thin text-xs">AEA线上教育平台和体验中心</span>
            <span className="font-normal">管理平台</span>
          </div>
          </Link>
          {/* <Button variant="outline" size="icon" className="ml-auto h-8 w-8">
            <Bell className="h-4 w-4"/>
            <span className="sr-only">Toggle notifications</span>
          </Button> */}
        </div>
        <div className="flex-1">
          <nav className="grid items-start px-2 text-sm font-medium lg:px-4">
            {navItems.map((item) => (
              <NavItem key={item.label} {...item} active={item.active}/>
            ))}
          </nav>
        </div>
      </div>
    </div>
  );
}