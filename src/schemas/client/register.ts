import { z } from "zod";

// 手机号验证正则表达式
const phoneRegex = /^1[3-9]\d{9}$/;

// 验证码请求Schema
export const verificationCodeSchema = z.object({
  phone: z.string().regex(phoneRegex, { message: "请输入有效的手机号" }),
});

// 第一步注册Schema（手机号和验证码）
export const phoneVerificationSchema = z.object({
  phone: z.string().regex(phoneRegex, { message: "请输入有效的手机号" }),
  verificationCode: z.string().min(4, { message: "请输入验证码" }),
  privacyAgreed: z.boolean().refine((val) => val === true, {
    message: "必须同意隐私条款才能注册",
  }),
});

// 医生注册Schema（完整信息）
export const doctorRegisterSchema = z.object({
  name: z.string().min(1, { message: "姓名不能为空" }),
  hospitalName: z.string().min(1, { message: "医院名称不能为空" }),
  hospitalId: z.string().min(1, { message: "请选择医院" }),
  email: z.string().email({ message: "请输入有效的邮箱地址" }),
  phone: z.string().regex(phoneRegex, { message: "请输入有效的手机号" }),
  verificationCode: z.string().min(4, { message: "请输入验证码" }),
  privacyAgreed: z.boolean().refine((val) => val === true, {
    message: "必须同意隐私条款才能注册",
  }),
  accessConditionsAgreed: z.boolean().refine((val) => val === true, {
    message: "必须承诺符合访问条件才能注册",
  }),
  internalAgreed: z.boolean().optional(),
});

// 导出类型
export type VerificationCodeInput = z.infer<typeof verificationCodeSchema>;
export type PhoneVerificationInput = z.infer<typeof phoneVerificationSchema>;
export type DoctorRegisterInput = z.infer<typeof doctorRegisterSchema>;
