import { $Enums } from "@prisma/client";
import { z } from "zod";


export const locationEmailSchema = z.string().email();
  
export const  locationSchema = z.object({
    id: z.number().optional(),
    name: z.string().min(1, { message: "地点为必填项" }),
    description: z.string().optional(),
    adminEmails: z.array(locationEmailSchema).optional(),
    notifyPolicy: z.number().optional(),
    autoConfirmPolicy: z.nativeEnum($Enums.AutoConfirmPolicy).optional(),
    virtual: z.boolean(),
    visiable: z.boolean(),
  })
