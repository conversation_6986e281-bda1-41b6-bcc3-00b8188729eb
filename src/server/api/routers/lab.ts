import { createTRPCRouter,withUserGroups } from "~/server/api/trpc";
import { z } from "zod";
import prisma from "~/db";
import { locationSchema } from "~/schemas/location";
import { $Enums } from "@prisma/client";

export const localtionRouter = createTRPCRouter({
  getLocaltions:  withUserGroups([
    $Enums.UserGroup.SYSADMIN,
    $Enums.UserGroup.ADMIN,
    $Enums.UserGroup.SUPERADMIN,
  ])
  .query(async () => {
    const data = await prisma.location.findMany({
      select: {
        id: true,
        name: true,
        description: true,
        adminEmails: true,
        autoConfirmPolicy: true,
        notifyPolicy: true,
        virtual: true,
        visiable: true,
        createdAt: true,
        updatedAt: true
      }
    })
    return {
      status: 'success',
      data: data
    }
  }),
  saveLocation:  withUserGroups([
    $Enums.UserGroup.SYSADMIN,
    $Enums.UserGroup.SUPERADMIN,
  ])
    .input(locationSchema)
    .mutation(async ({ input }) => {
      const { id, ...data } = input;
      await prisma.location.upsert({
        where: {
          id: id ?? -1
        },
        update: data,
        create: data
      })
      return { status: 'success'}
    }),


  deleteLocation:  withUserGroups([
    $Enums.UserGroup.SYSADMIN,
    $Enums.UserGroup.SUPERADMIN,
  ])
    .input(z.object({
      id: z.number(),
    }))
    .mutation(async ({ input }) => {
      const {id} =  input;
      const data = await  prisma.location.findFirst({
        select: {
          appointmentProjects: {
            select: {id: true}
          }
        },
        where: { id: id }
      })
      if (data?.appointmentProjects){
        return { status: 'error'}
      }
      const deletedLocation = await prisma.location.delete({
        where: { id: id },
      });
      return { status: 'success', location: deletedLocation };
    }),
})
