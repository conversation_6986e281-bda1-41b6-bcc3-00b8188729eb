import { $Enums, Prisma } from "@prisma/client";
import { z } from "zod";
import prisma from "~/db";

import { appointmentProjectSchema, CreateProjectInput, appointmentProjectExtraOptionSchema } from "~/schemas/appointment";
import { paginationSchema } from "~/schemas/pagination";
import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
  withUserGroups,
} from "~/server/api/trpc";


export const projectRouter = createTRPCRouter({

  createExtraOption: withUserGroups([$Enums.UserGroup.ADMIN, $Enums.UserGroup.SUPERADMIN])
    .input(appointmentProjectExtraOptionSchema)
    .mutation(async ({ input }) => {
      return prisma.appointmentProjectExtraOption.create({
        data: input
      });
    }),
  updateExtraOption: withUserGroups([$Enums.UserGroup.ADMIN, $Enums.UserGroup.SUPERADMIN])
    .input(appointmentProjectExtraOptionSchema)
    .mutation(async ({ input }) => {
      return prisma.appointmentProjectExtraOption.update({
        where: { id: input.id },
        data: input
      });
    }),
  deleteExtraOption: withUserGroups([$Enums.UserGroup.ADMIN, $Enums.UserGroup.SUPERADMIN])
    .input(z.number())
    .mutation(async ({ input }) => {

      const res = await prisma.appointmentProjectExtraOption.findFirst({
        select: {
          appointmentProject: {
            select: { id: true }
          }
        },
        where: {
          id: input
        }
      });

      if (res?.appointmentProject && res?.appointmentProject.length > 0) {
        return {
          status: "error"
        }
      }


      await prisma.appointmentProjectExtraOption.delete({
        where: { id: input }
      });
      return {
        status: "success"
      }
    }),
  getExtraOptions: withUserGroups([$Enums.UserGroup.ADMIN, $Enums.UserGroup.SUPERADMIN])
    .query(async ({ input }) => {
      return prisma.appointmentProjectExtraOption.findMany();
    }),

  createOrUpdateProject: withUserGroups([$Enums.UserGroup.ADMIN, $Enums.UserGroup.SUPERADMIN])
    .input(appointmentProjectSchema)
    .mutation(async ({ input }) => {
  
      const { id, ...data } = input;

      // Check if it had been attached to a appointment

      if (id) {
        const existingAppointmentProject = await prisma.appointmentProject.findFirst({
          where: { 
            id
          },
          select: {
            type: true,
            optionId: true,
            appointments: {
              select: {
                id: true // Only fetching the ID to check existence
              },
              take: 1 // Optimize by fetching only one related record
            }
          }
        })
        if (existingAppointmentProject?.appointments.length && (
          existingAppointmentProject.type !== data.type || existingAppointmentProject.optionId !== ( data.optionId ?? null)
        )){
          return {
            status: "error",
            existingAppointmentProject,
            message: "该预约项目下存在已存在预约，无法修改项目类型及附加选项"
          }
        }
      }

      // Check for existing project with same locationId and type
      const whereCondition = {
        locationId: input.locationId,
        type: input.type,
        ...(input.optionId ? { optionId: input.optionId } : {}),
        ...(id ? { NOT: { id } } : {})
      };
      const existingProject = await prisma.appointmentProject.findFirst({
        select: {id: true},
        where: whereCondition
      });

      if (existingProject?.id) {
        return {
          status: "error",
          message: "请检查该地点下是否存在相同类型的预约项目"
        }
      }
      // // If no duplicate found, create the new project
      await prisma.appointmentProject.upsert({
        where: {
          id: id ?? -1
        },
        update: {
          name: data.name,
          description: data.description,
          details: data.details,
          type: data.type,
          option: data.optionId ? {
            connect :  {
              id: data.optionId
            }
          } : { disconnect: true }
        } ,
        create: data
      });
      return {
        status: "success",
        whereCondition
      }
    }),

  getAllProjects: withUserGroups([$Enums.UserGroup.ADMIN, $Enums.UserGroup.SUPERADMIN])
    .input(paginationSchema.extend({
      locationId: z.number().optional(),
      keyword: z.string().optional()
    }))
    .query(async ({ input }) => {
      const { page, pageSize, locationId, keyword } = input;
      const whereCondition = {
        ...(locationId ? { locationId } : {}),
        ...(keyword ? { name: { contains: keyword } } : {})
      } ;
      const projects = await prisma.appointmentProject.findMany({
        where: whereCondition,
        skip: (page - 1) * pageSize,
        take: pageSize,
        select: {
          id: true,
          name: true,
          description: true,
          details: true,
          type: true,
          optionId: true,
          option: {
            select: {
              id: true,
              name: true
            }
          },
          locationId: true,
          createdAt: true,
          updatedAt: true
        }
      });

      const totalCount = await prisma.appointmentProject.count({
        where: whereCondition
      });

      return {
        data: projects,
        totalCount,
        page,
        pageSize,
        totalPages: Math.ceil(totalCount / pageSize),
      };
    }),

  getProjectById: publicProcedure
    .input(z.number())
    .query(({ input }) => {
      return prisma.appointmentProject.findUnique({
        where: { id: input },
        include: {
          location: {
            select: {
              name: true
            }
          }
        }
      });
    }),



  deleteProjectById: protectedProcedure
    .input(z.number())
    .mutation(async ({ input }) => {
      const existingAppointment = await prisma.appointmentProject.findFirst({
        where: { id: input },
        select: {
          appointments: {
            select: {
              id: true // Only fetching the ID to check existence
            },
            take: 1 // Optimize by fetching only one related record
          }
        }
      })
      if (existingAppointment?.appointments.length) {
        return {
          status: "error",
          message: "请预约项目下存在已存在预约，无法被删除"
        }
      }
      await prisma.appointmentProject.delete({
        where: { id: input },
      });
      return {
        status: "success"
      }
    }),
});