import { $Enums, Prisma } from "@prisma/client";
import { createTRPCRouter, withUserGroups } from "../../trpc";
import { paginationSchema } from "~/schemas/pagination";
import { z } from "zod";
import prisma from "~/db";
import { resetTheCacheForAppointment } from "../common/cache";
import { autoUpdateTheStatusForLiveAndTour } from "../common/appointments";
import { sendEmail } from "~/service";
import { format } from "date-fns";

export const mainRouter = createTRPCRouter({
  getAllAppointments: withUserGroups([
    $Enums.UserGroup.ADMIN,
    $Enums.UserGroup.SUPERADMIN,
  ])
    .input(
      paginationSchema.extend({
        locationId: z.number().int().optional(),
        status: z.nativeEnum($Enums.AppointmentStatus),
        projectId: z.number().int().optional(),
        sortBy: z.string().default("createdAt"),
        sortOrder: z.string().default("desc"),
      }),
    )
    .query(async ({ input }) => {
      const { page, pageSize, locationId, status, projectId, sortBy, sortOrder } = input;

      const _sortBy = ["startTime", "createdAt"].includes(sortBy) ? sortBy : "createdAt";
      const _sortOrder: Prisma.SortOrder = ["asc", "desc"].includes(sortOrder) ? sortOrder as Prisma.SortOrder : "desc";

      const _orderBy: Prisma.AppointmentOrderByWithRelationInput[] = [];

      if (_sortBy === "startTime") {
        _orderBy.push({
          timeSlot: {
            startTime: _sortOrder,
          },
        });
      }

      if (_sortBy === "createdAt") {
        _orderBy.push({
          createdAt: _sortOrder,
        });
      }

      const whereCondition: Prisma.AppointmentWhereInput = {
        appointmentProject: {
          ...(projectId ? { id: projectId } : {}),
          ...(locationId ? { locationId: locationId } : {}),
        },
        status: status,
      };


      await prisma.$transaction(async (tx) => {
        await autoUpdateTheStatusForLiveAndTour(tx);
      });

      const appointments = await prisma.appointment.findMany({
        skip: (page - 1) * pageSize,
        take: pageSize,
        select: {
          id: true,
          remark: true,
          customersCount: true,
          timeSlot: {
            select: {
              startTime: true,
              endTime: true,
            },
          },
          appointmentProject: {
            select: {
              name: true,
              type: true,
            },
          },
          AppointmentOperationLog: {
            select: {
              createdBy: {
                select: {
                  id: true,
                  name: true,
                }
              }
            },
            where: {
              status: $Enums.AppointmentStatus.CREATED,
            },
            take: 1,
          },
          createdAt: true,
        },
        where: whereCondition,
        orderBy: _orderBy,
      });
      const totalCount = await prisma.appointment.count({
        where: whereCondition,
      });
      return {
        data: appointments,
        totalCount,
        page,
        pageSize,
        totalPages: Math.ceil(totalCount / pageSize),
      };
    }),

  getAppointmentById: withUserGroups([
    $Enums.UserGroup.ADMIN,
    $Enums.UserGroup.SUPERADMIN,
  ])
    .input(
      z.object({
        id: z.number().int(),
      }),
    )
    .query(async ({ input }) => {
      const { id } = input;
      const data = await prisma.appointment.findFirst({
        where: {
          id: id,
        },
        select: {
          id: true,
          remark: true,
          status: true,
          customersCount: true,
          timeSlot: {
            select: {
              startTime: true,
              endTime: true,
            },
          },
          appointmentProject: {
            select: {
              name: true,
            },
          },
          AppointmentOperationLog: {
            select: {
              id: true,
              createdBy: {
                select: {
                  id: true,
                  name: true,
                },
              },
              status: true,
              message: true,
              createdAt: true,
              createdBySystem: true,
              verification: {
                select: {
                  appointmentVerificationFile: {
                    select: {
                      url: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              id: "asc",
            },
          },
          _count: {
            select: {
              customers: true,
            },
          },
          createdAt: true,
        },
      });
      return data;
    }),
  getAllCustomers: withUserGroups([
    $Enums.UserGroup.ADMIN,
    $Enums.UserGroup.SUPERADMIN,
  ])
    .input(
      z.object({
        status: z.nativeEnum($Enums.AppointmentStatus),
        locationId: z.coerce.number().optional(),
      }),
    )
    .query(async ({ input }) => {
      const { status, locationId } = input;
      const data = await prisma.customer.findMany({
        where: {
          appointments: {
            status: status,
            appointmentProject: {
              ...(locationId ? { locationId: locationId } : {}),
            },
          },
        },
        select: {
          id: true,
          name: true,
          contactInfo: true,
          hospital: {
            select: {
              sfid: true,
              name: true,
            },
          },
          appointments: {
            select: {
              id: true,
              timeSlot: {
                select: {
                  startTime: true,
                  endTime: true,
                },
              },
              appointmentProject: {
                select: {
                  name: true,
                  type: true,
                },
              },
              AppointmentOperationLog: {
                select: {
                  createdBy: {
                    select: {
                      name: true,
                    },
                  },
                },
                where: {
                  status: $Enums.AppointmentStatus.CREATED,
                },
                take: 1
              }
            },
          },
        },
      });
      return data;
    }),

  getAppointmentCustomers: withUserGroups([
    $Enums.UserGroup.ADMIN,
    $Enums.UserGroup.SUPERADMIN,
  ])
    .input(
      z.object({
        id: z.coerce.number().optional()
      }),
    )
    .query(async ({ input }) => {
      const { id } = input;

      const data = await prisma.appointment.findUnique({
        where: {
          id: id,
        },
        select: {
          appointmentProject: {
            select: {
              name: true,
              type: true,
            },
          },
          customers: {
            select: {
              id: true,
              name: true,
              contactInfo: true,
              hospital: {
                select: {
                  name: true,
                  sfid: true,
                },
              },
              createdAt: true,
            }
          },
          timeSlot: {
            select: {
              startTime: true,
              endTime: true,
            },
          },
          internalCustomers: {
            select: {
              id: true,
              name: true,
              department: true,
              createdAt: true,
            }
          },
          AppointmentOperationLog: {
            where: {
              status: $Enums.AppointmentStatus.CREATED,
            },
            select: {
              createdBy: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
            take: 1,
          }
        }
      })
      return {
        status: "success",
        data,
      };
    }),
  addAppointmentOperationLog: withUserGroups([
    $Enums.UserGroup.ADMIN,
    $Enums.UserGroup.SUPERADMIN,
  ])
    .input(
      z.object({
        id: z.number().int(),
        status: z.nativeEnum($Enums.AppointmentStatus),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const { id: appointmentId, status } = input;
      const userId: string = ctx.session.user.id;
      if (!userId) {
        throw new Error("please check user infomation!");
      }
      const res = await prisma.$transaction(async (tx) => {
        const log = await tx.appointmentOperationLog.create({
          data: {
            appointmentId: appointmentId,
            status: status,
            userId: userId,
          },
        });
        const appointment = await tx.appointment.update({
          where: { id: appointmentId },
          data: { status },
          select: {
            timeSlot: {
              select: {
                startTime: true,
                endTime: true,
              }
            },
            customersCount: true,
            appointmentProject: {
              select: {
                name: true,
                location: {
                  select: {
                    adminEmails: true,
                    notifyPolicy: true,
                  }
                }
              }
            }
          }
        });

        // 拒绝预约, 删除缓存
        if (status === $Enums.AppointmentStatus.REJECTED) {
          // 更新 remainCapacity

          await resetTheCacheForAppointment(tx, appointmentId);
        }
        // 检查 notifyPolicy
        const project = appointment.appointmentProject;
        if (project?.location.adminEmails && project.location.notifyPolicy && project.location.notifyPolicy & 2) {
          if (status === $Enums.AppointmentStatus.CONFIRMED) {
            // 找出创建人
            const log = await tx.appointmentOperationLog.findFirst({
              where: {
                appointmentId: appointmentId,
                status: $Enums.AppointmentStatus.CREATED,
              },
              select: {
                createdBy: {
                  select: {
                    name: true,
                  }
                }
              }
            })
            // 发送消息提醒
            await sendEmail(
              project.location.adminEmails,
              "预约确认提醒",
              {
                projectName: project.name,
                creator: log?.createdBy?.name ?? "-",
                timePeriod: `${format(appointment.timeSlot.startTime, 'yyyy-MM-dd HH:mm')} - ${format(appointment.timeSlot.endTime, 'yyyy-MM-dd HH:mm')}`,
                peopleCount: appointment.customersCount.toString(),
                remark: "",
                approvalLink: ""
              }
            );
          }
        }
        return log;
      });
      return {
        status: res.id ? "success" : "error",
      };
    }),
  addInternalCustomer: withUserGroups([
    $Enums.UserGroup.ADMIN,
    $Enums.UserGroup.SUPERADMIN,
  ])
    .input(
      z.object({
        id: z.number().int(),
        name: z.string().min(1),
        department: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const { id, name, department } = input;
      try {
        await prisma.$transaction(async (tx) => {
          // check if external customer exists
          const externalCustomer = await tx.customer.findFirst({
            where: {
              appointmentId: id,
            },
          });
          if (externalCustomer) {
            throw new Error("预约项目已存在外部客户");
          }
          await tx.internalCustomer.create({
            data: {
              name: name,
              department: department,
              appointments: {
                connect: {
                  id: id,
                },
              },
            },
          });
          await tx.appointment.update({
            where: { id: id },
            data: { customersCount: { increment: 1 } },
          });
        });
        return {
          status: "success",
        };
      } catch (error) {
        return {
          status: "error",
          message: error instanceof Error ? error.message : "系统错误",
        };
      }
    }),
  addCustomer: withUserGroups([
    $Enums.UserGroup.ADMIN,
    $Enums.UserGroup.SUPERADMIN,
  ])
    .input(
      z.object({
        id: z.number().int(),
        name: z.string().min(1),
        hospital: z.string(),
        contactInfo: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const { id, name, hospital, contactInfo } = input;
      // Prepare search condition
      const checkCondition = Number(hospital)
        ? { sfid: Number(hospital) }
        : { name: hospital };

      const hospitalData = await prisma.hospital.findFirst({
        where: checkCondition,
      });

      if (hospitalData) {
        // user transaction to create customer and update appointment's customers count
        const res = await prisma.$transaction(async (tx) => {
          await tx.customer.create({
            data: {
              name: name,
              contactInfo: contactInfo,
              appointments: {
                connect: {
                  id: id,
                },
              },
              hospital: {
                connect: {
                  id: hospitalData.id,
                },
              },
            },
          });
          await tx.appointment.update({
            where: { id: id },
            data: { customersCount: { increment: 1 } },
          });
        });
        return {
          status: "success",
        };
      }
      return {
        status: "error",
        message: `医院数据不存在: ${hospital}`,
      };
    }),

  deleteCustomer: withUserGroups([
    $Enums.UserGroup.ADMIN,
    $Enums.UserGroup.SUPERADMIN,
  ])
    .input(
      z.object({
        id: z.coerce.number().int(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const { id } = input;
      // 只有状态不为完成的预约才能删除客户信息
      await prisma.$transaction(async (tx) => {
        const customer = await tx.customer.delete({
          where: { 
            id: id
          },
        });
        const appointment = await tx.appointment.findFirst({
          select: {
            id: true,
            status: true
          },
          where: {
            id: customer.appointmentId,
            status: {
              notIn: [
                $Enums.AppointmentStatus.CHECKED
              ],
            },
            appointmentProject: {
              type: $Enums.AppointmentProjectType.TRAINING,
            },
          },
        });
        if (!appointment) {
          throw new Error("预约状态不正确, 或预约项目类型不正确");
        }
         await tx.appointment.update({ // 更新预约人数
          where: { 
            id:  customer.appointmentId
          },
          data: { customersCount: { decrement: 1 } },
        });

        await tx.appointmentOperationLog.create({
          data: {
            appointmentId: appointment.id,
            status: appointment.status,
            message: `删除 1 条客户信息 : ${customer.name}/${customer.hospitalId}/${customer.contactInfo}`,
            userId: ctx.session.user.id,
          },
        });
        return;
      });
      return {
        status: "success",
      };
    }),
    deleteInternalCustomer: withUserGroups([
      $Enums.UserGroup.ADMIN,
      $Enums.UserGroup.SUPERADMIN,
    ])
      .input(
        z.object({
          id: z.coerce.number().int(),
        }),
      )
      .mutation(async ({ input, ctx }) => {
        const { id } = input;
        await prisma.$transaction(async (tx) => {
          const internalCustomer = await tx.internalCustomer.delete({
            where: { id: id },
          });
          const appointment = await tx.appointment.findFirst({
            where: { 
              id: internalCustomer.appointmentId, 
              status: {
                notIn: [
                  $Enums.AppointmentStatus.CHECKED
                ],
              },
              appointmentProject: {
                type: $Enums.AppointmentProjectType.TRAINING,
              },
            },
          });
          if (!appointment) {
            throw new Error("预约状态不正确, 或预约项目类型不正确");
          }
          await tx.appointment.update({
            where: { id: appointment.id },
            data: { customersCount: { decrement: 1 } },
          });
          await tx.appointmentOperationLog.create({
            data: {
              appointmentId: appointment?.id ?? 0,
              status: appointment?.status ?? $Enums.AppointmentStatus.CREATED,
              message: `删除 1 条内部客户信息 : ${internalCustomer.name}/${internalCustomer.department}`,
              userId: ctx.session.user.id,
            },
          });
        });
        return {
          status: "success",
        };
      }),
    
    

  getInfo: withUserGroups([
    $Enums.UserGroup.ADMIN,
    $Enums.UserGroup.SUPERADMIN,
    $Enums.UserGroup.SYSADMIN,
  ]).query(async () => {
    const count1 = await prisma.appointment.count();
    const count2 = await prisma.customer.count();
    const count3 = await prisma.appointment.count({
      where: {
        status: $Enums.AppointmentStatus.CHECKED,
      },
    });
    return [count2, count1, count3];
  }),

  dataGroupByQuarter: withUserGroups([
    $Enums.UserGroup.ADMIN,
    $Enums.UserGroup.SUPERADMIN,
    $Enums.UserGroup.SYSADMIN,
  ])
    .input(
      z.object({
        year: z.number().int(),
      }),
    )
    .query(async ({ input }) => {
      const { year } = input;

      // hadle invalid year first
      if (year < 2024 || year > new Date().getFullYear()) {
        throw new Error("Invalid year");
      }

      const result = await prisma.$queryRaw<{
        locationName: string
        year: number
        quarter: number
        appointmentCount: bigint
      }[]>`
SELECT 
  l.name AS "locationName",
  EXTRACT(YEAR FROM ts."startTime") AS "year",
  CEIL(EXTRACT(MONTH FROM ts."startTime") / 3) AS "quarter",
  COUNT(a.id) AS "appointmentCount"
FROM 
  "Appointment" a
JOIN 
  "AppointmentProject" ap ON a."appointmentProjectId" = ap.id
JOIN 
  "Location" l ON ap."locationId" = l.id
JOIN
  "TimeSlot" ts ON a."timeSlotId" = ts.id  -- 新增：关联 TimeSlot 表
WHERE
  EXTRACT(YEAR FROM ts."startTime") = ${year}  -- 更新：使用 TimeSlot 的 startTime 进行年份过滤
GROUP BY 
  l.name, 
  "year",  -- 更新：按别名 "year" 分组
  "quarter" -- 更新：按别名 "quarter" 分组
ORDER BY
  l.name, 
  "year",
  "quarter";
  `;

      const parsed = result.map((item) => ({
        ...item,
        appointmentCount: Number(item.appointmentCount), // Convert BigInt → number
      }));
      return parsed;
    }),

  dataGroupByDateRange: withUserGroups([
    $Enums.UserGroup.ADMIN,
    $Enums.UserGroup.SUPERADMIN,
    $Enums.UserGroup.SYSADMIN,
  ])
    .input(
      z.object({
        startDate: z.string().datetime(),
        endDate: z.string().datetime(),
        groupBy: z.enum(["day", "week", "month"]).default("day"),
      }),
    )
    .query(async ({ input }) => {
      const { startDate, endDate, groupBy } = input;

      // Validate date range
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (start >= end) {
        throw new Error("Start date must be before end date");
      }

      // Limit the date range to prevent performance issues
      const maxDays = 365; // 1 year max
      const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
      if (daysDiff > maxDays) {
        throw new Error(`Date range cannot exceed ${maxDays} days`);
      }

      let dateGroupExpression: string;
      let dateFormatExpression: string;

      switch (groupBy) {
        case "day":
          dateGroupExpression = `DATE(ts."startTime")`;
          dateFormatExpression = `TO_CHAR(ts."startTime", 'YYYY-MM-DD')`;
          break;
        case "week":
          dateGroupExpression = `DATE_TRUNC('week', ts."startTime")`;
          dateFormatExpression = `TO_CHAR(DATE_TRUNC('week', ts."startTime"), 'YYYY-MM-DD')`;
          break;
        case "month":
          dateGroupExpression = `DATE_TRUNC('month', ts."startTime")`;
          dateFormatExpression = `TO_CHAR(DATE_TRUNC('month', ts."startTime"), 'YYYY-MM')`;
          break;
        default:
          dateGroupExpression = `DATE(ts."startTime")`;
          dateFormatExpression = `TO_CHAR(ts."startTime", 'YYYY-MM-DD')`;
      }

      const result = await prisma.$queryRaw<{
        locationName: string
        dateGroup: string
        appointmentCount: bigint
        status: string
      }[]>`
SELECT 
  l.name AS "locationName",
  ${dateFormatExpression} AS "dateGroup",
  COUNT(a.id) AS "appointmentCount",
  a.status AS "status"
FROM 
  "Appointment" a
JOIN 
  "AppointmentProject" ap ON a."appointmentProjectId" = ap.id
JOIN 
  "Location" l ON ap."locationId" = l.id
JOIN
  "TimeSlot" ts ON a."timeSlotId" = ts.id
WHERE
  ts."startTime" >= ${start}::timestamp
  AND ts."startTime" <= ${end}::timestamp
GROUP BY 
  l.name,
  ${dateGroupExpression},
  a.status
ORDER BY
  ${dateGroupExpression},
  l.name,
  a.status;
  `;

      const parsed = result.map((item) => ({
        ...item,
        appointmentCount: Number(item.appointmentCount),
      }));

      // Also get summary statistics for the date range
      const summaryResult = await prisma.$queryRaw<{
        totalAppointments: bigint
        totalCustomers: bigint
        completedAppointments: bigint
        locationName: string
      }[]>`
SELECT 
  l.name AS "locationName",
  COUNT(a.id) AS "totalAppointments",
  COALESCE(SUM(a."customersCount"), 0) AS "totalCustomers",
  COUNT(CASE WHEN a.status = 'CHECKED' THEN 1 END) AS "completedAppointments"
FROM 
  "Appointment" a
JOIN 
  "AppointmentProject" ap ON a."appointmentProjectId" = ap.id
JOIN 
  "Location" l ON ap."locationId" = l.id
JOIN
  "TimeSlot" ts ON a."timeSlotId" = ts.id
WHERE
  ts."startTime" >= ${start}::timestamp
  AND ts."startTime" <= ${end}::timestamp
GROUP BY 
  l.name
ORDER BY
  l.name;
  `;

      const summary = summaryResult.map((item) => ({
        ...item,
        totalAppointments: Number(item.totalAppointments),
        totalCustomers: Number(item.totalCustomers),
        completedAppointments: Number(item.completedAppointments),
      }));

      return {
        data: parsed,
        summary,
        dateRange: { startDate, endDate, groupBy },
      };
    }),

});


