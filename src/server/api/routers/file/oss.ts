import OSS from 'ali-oss';
import { z } from 'zod';
import {
  createTR<PERSON><PERSON>outer,
  protectedProcedure,
} from "~/server/api/trpc";
import { format } from "date-fns";
 
const region = process.env.ALIYUN_OSS_REGION;
const bucket = process.env.ALIYUN_OSS_BUCKET_NAME;

let ossClient: OSS | null = null;
let lastCredentialFetch = 0;
const credentialTTL = 60 * 60 * 1000; // 1 hour

async function getOssClient() {
  const now = Date.now();

  // Reuse the existing client if credentials are still valid
  if (ossClient && now - lastCredentialFetch < credentialTTL) {
    return ossClient;
  }

  let accessKeyId: string | undefined;
  let accessKeySecret: string | undefined;
  let stsToken: string | undefined;

  if (process.env.NODE_ENV === "production") {
    try {
      const { default: Credential, Config } = await import("@alicloud/credentials");
      const config = new Config({
        type: "ecs_ram_role",
        disableIMDSv1: true, // 推荐开启 IMDSv2 安全模式
      });
      const credentialClient = new Credential(config);
      const cred = await credentialClient.getCredential();
      accessKeyId = cred.accessKeyId;
      accessKeySecret = cred.accessKeySecret;
      stsToken = cred.securityToken;
    } catch (error) {
      console.error("Failed to retrieve credentials:", error);
      throw new Error("Failed to retrieve credentials");
    }
  } else {
    accessKeyId = process.env.ALIYUN_ACCESS_KEY_ID;
    accessKeySecret = process.env.ALIYUN_ACCESS_KEY_SECRET;
  }

  if (!accessKeyId || !accessKeySecret) {
    throw new Error("Missing ALIYUN_ACCESS_KEY_ID or ALIYUN_ACCESS_KEY_SECRET");
  }

  // Create and store a singleton OSS instance
  ossClient = new OSS({
    region,
    accessKeyId,
    accessKeySecret,
    bucket,
    secure: true,
    ...(stsToken ? { stsToken } : {}),
  });

  lastCredentialFetch = now;
  return ossClient;
}

export const ossRouter = createTRPCRouter({
  getPresignedUrl: protectedProcedure
    .input(
      z.object({
        filename: z.string(),
        contentType: z.string(),
      })
    )
    .mutation(async ({ input }) => {
      const ossClient = await getOssClient(); 
      try {
        // Generate date-based folder using date-fns
        const dateFolder = format(new Date(), "yyyyMMdd");
        const objectKey = `uploads/${dateFolder}/${input.filename}`;

        // Generate signed URL for PUT operation
        const signedUrl = ossClient.signatureUrl(objectKey, {
          method: 'PUT',
          'Content-Type': input.contentType,
          expires: 3600, // URL expires in 1 hour
        });

        return {
          success: true,
          url: signedUrl,
          objectKey: objectKey,
        };
      } catch (error) {
        console.error('Error generating signed URL:', error);
        throw new Error('Failed to generate upload URL');
      }
    }),
});


