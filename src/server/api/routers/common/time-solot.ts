import { $Enums } from "@prisma/client";
import type { RouterOutputs } from "../../root";
import { getDay, parseISO,   isBefore,  set, startOfDay, addDays, endOfDay, formatISO, format } from "date-fns";
import { toZonedTime } from "date-fns-tz";
import { HOLIDAY_DATES } from "./holiday";
import { TIMEZONE } from "~/constants/timezone";
import { isTimeRangeBetweenInclusive, isTimeRangeOverlap } from "~/lib/date";

export type TimeRange = [Date, Date];
type TimeRules = Omit<
    RouterOutputs["appointment"]["getTimeRulesByProjectId"]["data"][number], "createdAt" | "order"
    >[];

function findDatePeriod(targetDate: string) {
    if (!HOLIDAY_DATES.includes(targetDate)) return null;

    let start = targetDate;
    let end = targetDate;

    // Expand backward
    let prev = formatISO(addDays(parseISO(start), -1), { representation: 'date' });
    while (HOLIDAY_DATES.includes(prev)) {
        start = prev;
        prev = formatISO(addDays(parseISO(start), -1), { representation: 'date' });
    }

    // Expand forward
    let next = formatISO(addDays(parseISO(end), 1), { representation: 'date' });
    while (HOLIDAY_DATES.includes(next)) {
        end = next;
        next = formatISO(addDays(parseISO(end), 1), { representation: 'date' });
    }

    return { start, end };
}

function createNewDate(date: Date, targetDate: Date) {
    return set(targetDate, {
        year: date.getFullYear(),
        month: date.getMonth(),
        date: date.getDate(),
    });
}

 

function getValidTimeRangesFromTimeRuleV2(
    timeRules: TimeRules,
    start: Date,
    end: Date,
): TimeRange[] {
    
    const zonedStartTime = startOfDay(start);
    const zonedEndTime = endOfDay(end);

    let res: TimeRange[] = [];
 
    const lockedDates = timeRules.filter(rule => rule.type === $Enums.TimeRuleType.INCLUDE && rule.locked === true);
    const tempStart = new Set<Date>();
    const tempEnd = new Set<Date>();
    lockedDates.forEach(rule => {
        if (rule.scope === $Enums.TimeRuleScope.DAILY) {
            tempStart.add(rule.start!);
            tempEnd.add(rule.end!);
        }
    });

    for (const start of tempStart) {
        for (const end of tempEnd) {
            const _start = createNewDate(zonedStartTime, start);
            const _end = createNewDate(zonedEndTime, end);
            if (isBefore(_start, _end)) {
                res.push([_start, _end]);
            }
        }
    }
    tempStart.clear();
    tempEnd.clear();


    // console.log(timeRules);


 
 
    for (const timeRule of timeRules) {
        if(!timeRule.start && timeRule.scope === $Enums.TimeRuleScope.DATE_RANGE){
            continue;
        }
        const currentStart = timeRule.start ? toZonedTime(timeRule.start, TIMEZONE) : null;
        const currentEnd = !currentStart ? null : (
            timeRule.end ? toZonedTime(timeRule.end, TIMEZONE) : endOfDay(toZonedTime(currentStart, TIMEZONE))
        );


 
        if (timeRule.type === $Enums.TimeRuleType.INCLUDE) {
            // 已经在上方处理了每天
            if (timeRule.scope === $Enums.TimeRuleScope.DAILY) {
                continue;
            }
            if (timeRule.scope === $Enums.TimeRuleScope.DATE_RANGE) {
                // 如果当前时间在范围内，则添加到res中
                if ( 
                    currentStart && currentEnd &&
                    isTimeRangeOverlap(currentStart, currentEnd, zonedStartTime, zonedEndTime)
                ) {
                    res.push([currentStart, currentEnd]);
                }   
                continue;
            }
        }

        if (timeRule.type === $Enums.TimeRuleType.EXCLUDE) {
            res =  res.reduce((acc, [start, end]) => {
                if (
                    (
                        timeRule.scope === $Enums.TimeRuleScope.WEEKEND &&  
                        [getDay(start), getDay(end)].some(day => day === 6 || day === 0) 
                    ) || 
                    (
                        // 目前只检测首尾是否在节假日范围内
                        timeRule.scope === $Enums.TimeRuleScope.HOLIDAY &&
                        (findDatePeriod(format(start, 'yyyy-MM-dd')) || findDatePeriod(format(end, 'yyyy-MM-dd')))
                    ) || 
                    (
                        timeRule.scope === $Enums.TimeRuleScope.DATE_RANGE &&
                        (
                            currentStart && currentEnd &&
                            isTimeRangeBetweenInclusive(start, end,   startOfDay(currentStart), endOfDay(currentEnd))
                            // (isAfter(start, currentStart) && isBefore(start, currentEnd)) ||
                            // (isAfter(end, currentStart) && isBefore(end, currentEnd))
                        )
                    )
                ) {
                    return acc;
                }
                acc.push([start, end]);
                return acc;
            }, [] as TimeRange[]);
        }
    }
    return res;
}


export { getValidTimeRangesFromTimeRuleV2 }