import type { Prisma } from "@prisma/client";
import { $Enums } from "@prisma/client";
import { subDays } from "date-fns";

export async function autoUpdateTheStatusForLiveAndTour(tx: Prisma.TransactionClient) {
  const tempAppointments = await tx.appointment.findMany({
    select: {
      id: true,
      timeSlot: {
        select: {
          startTime: true,
        },
      },
    },
    where: {
      appointmentProject: {
        type: {
          in: [
            $Enums.AppointmentProjectType.TOUR,
            $Enums.AppointmentProjectType.LIVE,
          ],
        },
      },
      timeSlot: {
        startTime: {
          lte: new Date(),
        },
      },
      status: {
        in: [
          $Enums.AppointmentStatus.CREATED,
          $Enums.AppointmentStatus.CONFIRMED
        ],
      },
    },
  });
  // foreach each appointment, create a appointmentOperationLog
  const appointmentIds = [];
  const appointmentOperationLogs = [];
  for (const appointment of tempAppointments) {
    appointmentIds.push(appointment.id);
    appointmentOperationLogs.push({
      appointmentId: appointment.id,
      status: $Enums.AppointmentStatus.CHECKED,
      createdBySystem: true,
      createdAt: appointment.timeSlot.startTime,
    });
  }
  if (appointmentOperationLogs.length > 0) {
    await tx.appointmentOperationLog.createMany({
      data: appointmentOperationLogs,
    });
  }
  if (appointmentIds.length > 0) {
    await tx.appointment.updateMany({
      where: {
        id: {
          in: appointmentIds,
        },
      },
      data: {
        status: $Enums.AppointmentStatus.CHECKED,
      },
    });
  }
}


export async function autoRemoveTheStaleProjectTimeRules(tx: Prisma.TransactionClient) {
    await  tx.appointmentProjectTimeRule.deleteMany({
        where: {
            OR: [
                {
                    scope: $Enums.TimeRuleScope.DATE_RANGE,
                    end: {
                        lt: new Date()
                    }
                },
                {
                    scope: $Enums.TimeRuleScope.DATE_RANGE,
                    start: {
                        lt: subDays( new Date(), 1)
                    },
                    end:  null
                },
            ]
        }
    })
}
