// this is for the client side api
// use to get the time period for a project in a specific date

import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
  withUserGroups,
} from "../../../../trpc";
import { date, z } from "zod";
import prisma from "~/db";
// import { getValidTimeRangesFromTimeRule } from "../utils/time-slot.utils";
// import { checkTimeConflictsAndGetAvailableSlots, type CacheItem } from "../utils/time-conflict.utils";
import { TRPCError } from "@trpc/server";
import { parseISO, addDays } from "date-fns";
import { $Enums, UserGroup } from "@prisma/client";
import { getValidTimeRangesFromTimeRuleV2 } from "../../../common/time-solot";
import { checkTimeConflictsAndGetAvailableSlotsFromCache } from "../../../common/cache";
import { autoRemoveTheStaleProjectTimeRules } from "../../../common/appointments";
import { toZonedTime } from "date-fns-tz";
import { TIMEZONE } from "~/constants/timezone";

export const appointmentExtraRouter = createTRPCRouter({
  // 获取所有地点
  getLocations: protectedProcedure.query(async () => {
    const locations = await prisma.location.findMany({
      where: {
        visiable: true,
      },
      select: {
        id: true,
        name: true,
        description: true,
      },
    });

    return locations;
  }),

  getExtraOptions: protectedProcedure.query(async ({}) => {
    return prisma.appointmentProjectExtraOption.findMany();
  }),

  searchHospitals: publicProcedure
    .input(
      z
        .object({
          keyword: z.union([z.string(), z.number()]).optional(),
          limit: z.number().min(1).max(20).default(20),
          offset: z.number().min(0).default(0),
        })
        .optional(),
    )
    .query(async ({ input }) => {
      const { keyword, limit = 20, offset = 0 } = input ?? {};
      const numberSchema = z.preprocess(
        (val) => (typeof val === "string" ? Number(val) : val),
        z.number()
      );
      const result = numberSchema.safeParse(keyword);

      const where = keyword
      ? {
          OR: [
            { name: { contains: keyword.toString() } },
            ...(result.success ? [{ sfid: { equals: result.data } }] : []),
          ],
        }
      : undefined;

      const hospitals = await prisma.hospital.findMany({
        where: where,
        select: {
          id: true,
          sfid: true,
          name: true,
        },
        take: Math.min(limit, 20),
        skip: offset,
      });
      return { hospitals };
    }),

    searchHospitalsById: publicProcedure
    .input(z.number())
    .query(async ({ input }) => {
      const hospital = await prisma.hospital.findUnique({ where: { id: input }, select: {
        id: true,
        sfid: true,
        name: true,
      }  });
      return { hospital };
    }),

  // 获取医院（支持搜索和分页）
  getHospitals: protectedProcedure
    .input(
      z
        .object({
          search: z.string().optional(),
          limit: z.number().min(1).max(10000).default(20),
          offset: z.number().min(0).default(0),
        })
        .optional(),
    )
    .query(async ({ input }) => {
      const { search, limit = 20, offset = 0 } = input ?? {};

      // 构建查询条件，支持通过名称或ID搜索
      const where = search
        ? {
            OR: [
              { name: { contains: search } },
              // sfid是数字类型，这里只能通过name搜索
              { name: { contains: search } },
            ],
          }
        : undefined;

      // 查询总数
      const total = await prisma.hospital.count({ where });

      // 查询分页数据
      const hospitals = await prisma.hospital.findMany({
        where,
        select: {
          id: true,
          sfid: true,
          name: true,
        },
        take: limit,
        skip: offset,
      });

      return { hospitals, total };
    }),

  /**
   * 获取可预约时间段
   *
   * 该接口处理客户端预约系统的时间段查询功能，包含以下步骤：
   * 1. 验证项目是否存在
   * 2. 检查用户权限（普通用户与培训师有不同预约天数限制）
   * 3. 验证预约日期是否在允许范围内（普通用户30天，培训师90天）
   * 4. 获取项目相关的时间规则
   * 5. 根据时间规则获取有效时间范围
   * 6. 查询缓存数据检查时段冲突
   * 7. 应用限制规则（最大容量、互斥项目）筛选可用时间段
   *
   * @param projectId - 预约项目ID，用于获取特定项目的时间规则
   * @param date - 查询日期，格式为YYYY-MM-DD
   * @returns 可用的时间段列表及总数，每个时间段包含开始时间、结束时间和剩余容量
   */
  getTimeSlots: withUserGroups([
    $Enums.UserGroup.SALES,
    $Enums.UserGroup.TRAINER,
  ])
    .input(
      z.object({
        projectId: z.number(),
        startDate: z.string(),
        endDate: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const user = ctx.session.user;
      const { projectId, startDate, endDate } = input;

      // 1. 项目查询 - 只选择必要字段
      const project = await prisma.appointmentProject.findUnique({
        where: { id: projectId },
        select: {
          id: true
        },
      });

      if (!project) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "预约项目不存在或已禁用",
        });
      }

      // 2. 预约日期有效性检查
      const today = new Date();
      const start = toZonedTime(startDate, TIMEZONE);
      const end = toZonedTime(endDate, TIMEZONE);
      const isTrainerGroup = user?.usergroups.includes(UserGroup.TRAINER);
      const maxDays = isTrainerGroup ? 90 : 30;
      const maxAllowedDate = addDays(today, maxDays);

      if (end > maxAllowedDate) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `最多只能预约${maxDays}天内的时间`,
        });
      }

      // 3. 获取时间规则
      //3.1 删除已经失效的规则 ，获取剩余规则列表

      const timeRules = await prisma.$transaction(async (tx) => {
        await autoRemoveTheStaleProjectTimeRules(tx);
        return tx.appointmentProjectTimeRule.findMany({
          select: {
            id: true,
            start: true,
            end: true,
            locked: true,
            type: true,
            scope: true
          },
          where: {
            OR: [
              {
                projects: {
                  some: {
                    id: projectId,
                  },
                },
              },
              {
                locked: true,
              },
            ],
          },
          orderBy: {
            order: "asc",
          },
        });
      });


      // 4. 根据时间规则获取有效时间范围
      //const validTimeRanges = getValidTimeRangesFromTimeRule(timeRules, date);
      const validTimeRanges = getValidTimeRangesFromTimeRuleV2(timeRules, start, end);

      return await prisma.$transaction(async (tx) => {
        const data = await checkTimeConflictsAndGetAvailableSlotsFromCache(
          tx,
          validTimeRanges,
          projectId,
        );
        return data;
      });


    }),

  // 获取预约须知
  getAnnouncement: protectedProcedure
    .input(
      z.object({
        projectId: z.number(),
      }),
    )
    .query(async ({ input }) => {
      const { projectId } = input;

      const project = await prisma.appointmentProject.findUnique({
        where: { id: projectId },
        select: {
          id: true,
          name: true,
          description: true,
        },
      });

      if (!project) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "预约项目不存在",
        });
      }

      return {
        id: project.id,
        name: project.name,
        description: project.description ?? "", // 确保返回字符串，即使details为null
      };
    }),
});
