import { z } from "zod";
import { protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";
import prisma from "~/db";
import { AppointmentStatus, Prisma } from "@prisma/client";
import { resetTheCacheForAppointment } from "~/server/api/routers/common/cache";
import { autoUpdateTheStatusForLiveAndTour } from "../../../common/appointments";
import { sendEmail } from "~/service/email";
import { format } from "date-fns";
import { cancelSubscribeMessage} from "~/service";
// 签到请求的输入模型
const verifyAppointmentSchema = z.object({
  appointmentId: z.number(),
  verificationImg: z.string(),
  remark: z.string().optional(),
});

export const userAppointmentControllers = {
  // 获取当前用户的所有预约
  getUserAppointments: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(50).nullish(),
        cursor: z.number().nullish(), // 使用appointment.id作为cursor
      })
    )
    .query(async ({ ctx, input }) => {
    try {
      // 确保用户ID存在且是字符串类型
      const userId = ctx.session.user.id;
      const limit = input?.limit ?? 10; // 默认每页10条
      const { cursor } = input || {};

      const appointments = await prisma.$transaction(async(tx)=>{
        await autoUpdateTheStatusForLiveAndTour(tx);
        const data = await tx.appointment.findMany({
          where: {
            // 通过操作日志关联查询用户创建的预约
            AppointmentOperationLog: {
              some: {
                userId: userId,
              },
            },
            // 如果有cursor，则查询ID小于cursor的记录（因为是按创建时间降序排列）
            ...(cursor ? { id: { lt: cursor } } : {}),
          },
          include: {
            appointmentProject: {
              include: {
                location: true, // 包含地点信息
              }
            },
            timeSlot: true, // 包含时间槽信息
            customers: true, // 包含客户信息
          },
          orderBy: {
            createdAt: "desc", // 按创建时间降序排列
          },
          take: limit + 1, // 多取一条用于判断是否有下一页
        });
        return data;
      });

      // 判断是否有下一页
      let nextCursor: typeof cursor | undefined = undefined;
      if (appointments.length > limit) {
        // 移除多取的一条数据
        const nextItem = appointments.pop();
        // 使用最后一条数据的ID作为nextCursor
        nextCursor = nextItem!.id;
      }

      // 格式化数据以便前端使用
      const items = appointments.map((appointment) => {
        // 安全地访问关联数据
        const project = appointment.appointmentProject;
        const timeSlot = appointment.timeSlot;
        const customers = appointment.customers ?? [];
        const status = appointment.status;

        // 获取第一个客户名称作为预约人，如果没有则显示"未知"
        const bookedBy = customers.length > 0 ? customers[0]?.name ?? "未知" : "未知";
        // 获取客户数量作为预约人数
        const customerCount = appointment.customersCount;

        // 获取地点信息
        const locationName = project?.location?.name ?? "未知地点";

        // 直接使用数据库中的AppointmentStatus枚举值，不再进行映射
        // 这样前端可以直接使用枚举值进行比较和显示

        // 确保所有字段都存在
        return {
          id: appointment.id,
          projectName: project?.name || "未知项目",
          status: status, // 直接返回数据库中的AppointmentStatus枚举值
          appointmentDate: timeSlot ? timeSlot.startTime.toISOString().split('T')[0] : "-",
          startTime: timeSlot?.startTime || new Date(),
          endTime: timeSlot?.endTime || new Date(),
          remark: appointment.remark ?? "",
          bookedBy: bookedBy,
          customerCount: customerCount,
          locationName: locationName, // 添加地点信息
        };
      });

      // 返回结果和nextCursor
      return {
        items,
        nextCursor,
      };
    } catch (error) {
      console.error("获取用户预约失败:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "获取预约记录失败",
      });
    }
  }),

  // 取消预约
  cancelAppointment: protectedProcedure
    .input(
      z.object({
        appointmentId: z.number(),
        cancelReason: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const { appointmentId, cancelReason } = input;
        // 确保用户ID存在且是字符串类型
        const userId = ctx.session.user.id;

        // 查询预约是否存在且属于当前用户
        const appointment = await prisma.appointment.findFirst({
          where: {
            id: appointmentId,
            AppointmentOperationLog: {
              some: {
                userId: userId,
              },
            },
          },
          include: {
            timeSlot: true, // 包含时间槽信息，用于检查是否在3天内
            appointmentProject: {
              select: {
                name: true,
                location: {
                  select: {
                    name: true,
                    adminEmails: true,
                    notifyPolicy: true,
                  }
                }
              }
            },
            AppointmentOperationLog: {
              orderBy: {
                createdAt: "desc",
              },
              take: 1,
            },
          },
        });

        if (!appointment) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "预约不存在或不属于当前用户",
          });
        }

        // 检查是否在预约日期的3天内
        const appointmentDate = appointment.timeSlot?.startTime;
        if (!appointmentDate) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "预约时间信息不完整",
          });
        }

        // 计算预约日期与当前日期的天数差
        const currentDate = new Date();
        const daysDifference = Math.ceil(
          (appointmentDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24)
        );

        // 如果在3天内取消预约，必须提供取消原因
        if (daysDifference <= 3 && !cancelReason) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "取消3天内的预约需要提供取消原因",
          });
        }

        // 创建取消预约的操作日志
        await prisma.$transaction(async (tx) => {
          await tx.appointmentOperationLog.create({
            data: {
              userId: userId,
              appointmentId,
              message: cancelReason,
              status: AppointmentStatus.CANCELLED,
            },
          });
          await tx.appointment.update({
            where: { id: appointmentId },
            data: { status: AppointmentStatus.CANCELLED },
          });
          await resetTheCacheForAppointment(tx, appointmentId);
          return appointmentId;
        });

        // 发送取消邮件
        if (appointment.appointmentProject?.location.adminEmails && appointment.appointmentProject?.location.notifyPolicy) {
          const needSendEmail = (appointment.appointmentProject?.location.notifyPolicy & 4) !== 0;
          if (needSendEmail) {
            await sendEmail(appointment.appointmentProject?.location.adminEmails, "预约取消提醒", {
              projectName: appointment.appointmentProject?.name,
              creator: ctx.session.user.name ?? "-",
              timePeriod: `${format(appointment.timeSlot?.startTime, 'yyyy-MM-dd HH:mm')} - ${format(appointment.timeSlot?.endTime, 'yyyy-MM-dd HH:mm')}`,
              peopleCount: appointment.customersCount.toString(),
              remark: cancelReason ?? "-",
            });
          }
        }
        // 取消消息提醒
        
        await cancelSubscribeMessage(appointment.id.toString());
        
        return {
          success: true,
          message: "预约取消成功",
        };
      } catch (error) {
        console.error("取消预约失败:", error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "取消预约失败",
        });
      }
    }),

  // 获取可签到的预约列表
  getVerifiableAppointments: protectedProcedure.query(async ({ ctx }) => {
    try {
      const userId = ctx.session.user.id;

      // 查询用户的所有预约记录，过滤出可以签到的预约
      // 可签到预约的条件：状态为CONFIRMED且没有CHECKED的状态
      const appointments = await prisma.appointment.findMany({
        where: {
          AppointmentOperationLog: {
            some: {
              userId,
              // 排除已被取消的预约
              status: {
                not: AppointmentStatus.CANCELLED,
              },
            },
          },
        },
        include: {
          appointmentProject: {
            include: {
              location: true, // 包含地点信息
            }
          },
          timeSlot: true,
          customers: true, // 包含客户信息
          AppointmentOperationLog: {
            orderBy: {
              createdAt: "desc",
            },
            take: 1,
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      // 过滤出可签到的预约（已确认但未签到的）
      const verifiableAppointments = appointments.filter((appointment) => {
        const latestStatus = appointment.AppointmentOperationLog[0]?.status;
        return latestStatus === AppointmentStatus.CONFIRMED &&
               // 确保没有已签到的状态
               !appointments.some(a =>
                 a.id === appointment.id &&
                 a.AppointmentOperationLog.some(log => log.status === AppointmentStatus.CHECKED)
               );
      });

      // 格式化返回数据
      return verifiableAppointments.map((appointment) => {
        const project = appointment.appointmentProject;
        const timeSlot = appointment.timeSlot;
        const customers = appointment.customers ?? [];

        // 获取第一个客户名称作为预约人，如果没有则显示"未知"
        const bookedBy = customers.length > 0 ? customers[0]?.name ?? "未知" : "未知";
        // 获取客户数量作为预约人数
        const customerCount = customers.length;

        // 获取地点信息
        const locationName = project?.location?.name ?? "未知地点";

        return {
          id: appointment.id,
          projectName: project?.name || "未知项目",
          status: "confirmed",
          appointmentDate: timeSlot ? timeSlot.startTime.toISOString().split('T')[0] : "-",
          startTime: timeSlot?.startTime || new Date(),
          endTime: timeSlot?.endTime || new Date(),
          remark: appointment.remark ?? "",
          bookedBy: bookedBy,
          customerCount: customerCount,
          locationName: locationName, // 添加地点信息
        };
      });
    } catch (error) {
      console.error("获取可签到预约失败:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "获取可签到预约失败",
      });
    }
  }),

  // 提交预约签到
  verifyAppointment: protectedProcedure
    .input(verifyAppointmentSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const { appointmentId, verificationImg } = input;
        const userId = ctx.session.user.id;

        // 查询预约是否存在且属于当前用户
        const appointment = await prisma.appointment.findFirst({
          where: {
            id: appointmentId,
            AppointmentOperationLog: {
              some: {
                userId,
              },
            },
          },
          include: {
            AppointmentOperationLog: {
              orderBy: {
                createdAt: "desc",
              },
              take: 1,
            },
          },
        });

        if (!appointment) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "预约不存在或不属于当前用户",
          });
        }

        // 检查是否已被取消或已签到
        const latestStatus = appointment.AppointmentOperationLog[0]?.status;

        if (latestStatus === AppointmentStatus.CANCELLED) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "该预约已被取消，无法签到",
          });
        }

        if (latestStatus === AppointmentStatus.CHECKED) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "该预约已经签到，请勿重复操作",
          });
        }

        // 开启事务，保证数据一致性
        return await prisma.$transaction(async (tx) => {
          // 创建签到状态的操作日志
          const operationLog = await tx.appointmentOperationLog.create({
            data: {
              userId,
              appointmentId,
              status: AppointmentStatus.CHECKED,
            },
          });

          // 使用事务对象创建签到记录，与操作日志建立关联 - 直接设置为已批准
          // const now = new Date();
          // const verification = await tx.$queryRaw`
          //   INSERT INTO "AppointmentVerification" ("operationLogId", "verificationImg", "remark", "isApproved", "approvedAt", "approvedBy", "createdAt", "updatedAt")
          //   VALUES (${operationLog.id}, ${verificationImg}, ${remark ?? null}, true, ${now}, ${userId}, NOW(), NOW())
          //   RETURNING id
          // `;

          const verification =  await tx.appointmentVerification.create({
            data: {
              operationLog: {
                connect: {
                  id: operationLog.id
                }
              },
              appointmentVerificationFile: {
                connect: {
                  id: Number(verificationImg)
                }
              }
            }
          })

          // 获取插入的ID
          const verificationId = verification.id;

          // 同步预约的状态
          await tx.appointment.update({
            where: { id: appointmentId },
            data: {
              status: AppointmentStatus.CHECKED
            },
          });

          return {
            success: true,
            message: "预约签到成功，已自动批准",
            verificationId: verificationId,
            verifiedAt: operationLog.createdAt,
          };
        });
      } catch (error) {
        console.error("预约签到失败:", error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "预约签到失败",
        });
      }
    }),

  // 获取预约签到历史
  getVerificationHistory: protectedProcedure
    .input(
      z.object({
        appointmentId: z.number(),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        const { appointmentId } = input;
        const userId = ctx.session.user.id;

        // 查询预约是否存在且属于当前用户
        const appointment = await prisma.appointment.findFirst({
          where: {
            id: appointmentId,
            AppointmentOperationLog: {
              some: {
                userId,
              },
            },
          },
        });

        if (!appointment) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "预约不存在或不属于当前用户",
          });
        }

        // 查询该预约的所有签到记录
        const verificationLogs = await prisma.appointmentOperationLog.findMany({
          where: {
            appointmentId,
            status: AppointmentStatus.CHECKED,
          },
          include: {
            createdBy: {
              select: {
                name: true,
                username: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        });

        // 使用原生 SQL 查询过滤签到记录
        // 创建包含所有操作日志ID的字符串
        const operationLogIds = verificationLogs.map(log => log.id);

        // 使用安全的参数化查询
        const verifications = await prisma.$queryRaw<Array<{
          id: number;
          operationLogId: number;
          verificationImg: string;
          isApproved: boolean;
          approvedBy: string | null;
          approvedAt: Date | null;
          remark: string | null;
        }>>(Prisma.sql`
          SELECT * FROM "AppointmentVerification"
          WHERE "operationLogId" IN (${Prisma.join(operationLogIds)})
        `);

        // 格式化返回数据
        return verificationLogs.map((log) => {
          // 查找对应的验证记录
          const verification = verifications.find((v) => v.operationLogId === log.id);

          return {
            id: verification?.id,
            verificationImg: verification?.verificationImg,
            verifiedAt: log.createdAt,
            verifiedBy: log.createdBy?.name ?? log.createdBy?.username ?? 'unknown',
            isApproved: verification?.isApproved ?? false,
            approvedAt: verification?.approvedAt,
            approvedBy: verification?.approvedBy,
            remark: verification?.remark,
          };
        });
      } catch (error) {
        console.error("获取签到历史失败:", error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "获取签到历史失败",
        });
      }
    }),
};
