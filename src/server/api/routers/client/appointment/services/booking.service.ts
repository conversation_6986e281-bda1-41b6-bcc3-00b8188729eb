/* eslint-disable @typescript-eslint/no-unused-vars */
import prisma from "~/db";
import type { Prisma } from "@prisma/client";
import { $Enums } from "@prisma/client";
import { withUserGroups } from "~/server/api/trpc";
import { updateTheCacheWhenCreateAppointment } from "../../../common/cache";
import { z } from "zod";
import { createAppointmentSchema } from "~/schemas/client/appointment";
import { addDays, getDay } from "date-fns";
import { format, isAfter, isBefore } from "date-fns";
import { HOLIDAY_DATES } from "../../../common/holiday";
import { sendEmail } from "~/service/email";



type BookingInput = z.infer<ReturnType<typeof createAppointmentSchema>>;

/**
 * 查找或创建时间槽
 */
const getTimeSlot = async (tx: Prisma.TransactionClient, startTime: Date, endTime: Date) => {
  // 查找时间槽
  let timeSlot = await tx.timeSlot.findFirst({
    where: {
      startTime,
      endTime,
    },
  });

  // 如果时间槽不存在，创建新的时间槽
  timeSlot ??= await tx.timeSlot.create({
    data: {
      startTime,
      endTime,
    },
  });

  return timeSlot;
};



/**
 * 创建预约记录
 */
const createAppointmentRecord = async (
  tx: Prisma.TransactionClient,
  timeSlotId: number,
  projectId: number,
  remark: string,
  customersCount: number,
  status: $Enums.AppointmentStatus
) => {
  return tx.appointment.create({
    data: {
      timeSlotId,
      appointmentProjectId: projectId,
      remark: remark ?? "",
      customersCount: customersCount,
      status: status
    },
  });
};

/**
 * 创建客户记录
 */
const createCustomerRecords = async (
  tx: Prisma.TransactionClient,
  appointmentId: number,
  customers: BookingInput['customers'] = []
) => {
  if (!customers || customers.length === 0) return;
  await tx.customer.createMany({
    data: customers.map(customer => ({
      appointmentId,
      name: customer.name ?? "",
      hospitalId: Number(customer.hospitalId),
      contactInfo: customer.phone ?? "",
    }))
  });
};

/**
 * 创建操作日志
 */
const createOperationLog = async (
  tx: Prisma.TransactionClient,
  appointmentId: number,
  userId: string,
  autoConfirm = false
) => {
  const data = [
    {
      appointmentId,
      userId,
      status: $Enums.AppointmentStatus.CREATED // 默认为已创建状态
    },
    {
      appointmentId,
      status: $Enums.AppointmentStatus.CONFIRMED,
      createdBySystem: true
    }
  ];
  if (!autoConfirm) {
    data.pop();
  }
  await tx.appointmentOperationLog.createMany({
    data
  });
};

/**
 * 创建预约服务
 * 负责处理预约创建的业务逻辑
 */



export const createBooking = withUserGroups([
  $Enums.UserGroup.SALES,
  $Enums.UserGroup.TRAINER,
])
  .input(
    createAppointmentSchema(false).extend({
      startTime: z.string().datetime(),
      endTime: z.string().datetime(),
      projectId: z.number().int(),
    })
  )
  .mutation(async ({ ctx, input }) => {
    const userId = ctx.session.user.id; // 获取当前用户ID用于创建操作日志
    const { customers, count, remark, projectId, startTime: _startTime, endTime: _endTime } = input;



    const startTime = new Date(_startTime);
    const endTime = new Date(_endTime);

    // 获取项目详情
    // const project = await validateAndGetProject(projectId);

    const project = await prisma.appointmentProject.findUnique({
      where: { id: projectId },
      select: {
        type: true,
        name: true,
        location: {
          select: {
            id: true,
            autoConfirmPolicy: true,
            notifyPolicy: true,
            adminEmails: true,
          }
        }
      }
    });

    // 预约人数逻辑：
    // 1. 如果是 LIVE 类型且提供了 peopleCount，使用提供的值
    // 2. 其他类型或未提供 peopleCount，使用 customers 数组的长度（默认为1）
    let effectivePeopleCount = 0; // 默认值

    // 检查项目类型
    // 从 Prisma 返回的真实项目模型中读取类型
    const isLiveProject = project?.type === $Enums.AppointmentProjectType.LIVE;

    if (isLiveProject && count !== undefined) {
      // 对于 LIVE 项目，如果提供了 peopleCount，使用提供的值
      effectivePeopleCount = count;
    } else if (customers.length > 0) {
      // 对于非 LIVE 项目或未提供 peopleCount，使用 customers 长度
      effectivePeopleCount = customers.length;
    }


    return await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
      // 1. 查找或创建时间槽
      const timeSlot = await getTimeSlot(tx, startTime, endTime);
      // 2.创建

      // 2.1 确定该项目是否自动确认
      let autoConfirm = false;
      if (project?.location.autoConfirmPolicy === $Enums.AutoConfirmPolicy.ALWAYS) {
        autoConfirm = true;
      } else if (project?.location.autoConfirmPolicy === $Enums.AutoConfirmPolicy.PERIOD_NOT_MEET_WEEKEND_AND_HOLIDAY) {
        // 循环监测每一天是否包含节假日及周末
        let current = startTime;
        while (isBefore(current, endTime)) {
          if (HOLIDAY_DATES.includes(format(current, 'yyyy-MM-dd'))) {
            break;
          }
          if (getDay(current) === 0 || getDay(current) === 6) {
            break;
          }
          current = addDays(current, 1);
        }
        autoConfirm = true;
      }
      // 2.2 创建预约记录

      const appointmentStatus = autoConfirm ? $Enums.AppointmentStatus.CONFIRMED : $Enums.AppointmentStatus.CREATED;
      const appointment = await createAppointmentRecord(tx, timeSlot.id, projectId, remark ?? "", effectivePeopleCount, appointmentStatus);

      // 3. 更新缓存
      await updateTheCacheWhenCreateAppointment(tx, startTime, endTime, projectId, appointment.id);

      // 4. 创建预约人记录
      await createCustomerRecords(tx, appointment.id, customers);

      // 5. 创建操作日志
      await createOperationLog(tx, appointment.id, userId, autoConfirm);

      // 6. 发送邮件
      // 6.1 检查是否需要发送邮件
      if (project?.location.adminEmails && project?.location.notifyPolicy ) {
        // 发送邮件
        const needSendEmail = autoConfirm ? (project.location.notifyPolicy & 2) !== 0 : (project.location.notifyPolicy & 1) !== 0;
        if (needSendEmail) {
          const title = autoConfirm ? "预约确认提醒" : "预约审批提醒";
          await sendEmail(project?.location.adminEmails, title, {
            projectName: project.name,
            creator: ctx.session.user.name ?? "-",
            timePeriod: `${format(startTime, 'yyyy-MM-dd HH:mm')} - ${format(endTime, 'yyyy-MM-dd HH:mm')}`,
            peopleCount: effectivePeopleCount.toString(),
            remark: remark ?? "",
            approvalLink: autoConfirm ? "" : `${process.env.NEXTAUTH_URL}/admin/appointment/item?locationId=${project?.location.id}` 
          });
        }
      }

      //7.1 发送消息提醒
      //TODO: 发送消息提醒

      return {
        id: appointment.id,
        message: "预约创建成功",
      };
    });
  });
