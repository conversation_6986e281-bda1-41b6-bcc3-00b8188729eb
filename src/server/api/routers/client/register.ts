import { createTRPCRouter, publicProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { doctorRegisterSchema, verificationCodeSchema } from "~/schemas/client/register";
import prisma from "~/db";
import bcrypt from "bcrypt";
import { $Enums } from "@prisma/client";
import { z } from "zod";

// 生成随机验证码
function generateVerificationCode(): string {
  return Math.floor(1000 + Math.random() * 9000).toString();
}

// 发送短信验证码
async function sendSmsVerificationCode(phoneNumber: string, code: string): Promise<boolean> {
  try {
    // 构建请求URL和请求体
    const url = `${process.env.MESSAGE_TUNNEL_APP_BASE_URL}/messages/sms`;
    const requestBody = {
      message: {
        phone_number: phoneNumber,
        template_id: "SMS_484045242", // 短信模板ID
        data: {
          code: code,
        },
      },
    };

    // 发送请求
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${process.env.MESSAGE_TUNNEL_APP_TOKEN}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      console.error("短信发送失败:", await response.text());
      return false;
    }

    return true;
  } catch (error) {
    console.error("短信发送异常:", error);
    return false;
  }
}

// 创建注册路由
export const registerRouter = createTRPCRouter({
  // 发送验证码
  sendVerificationCode: publicProcedure
    .input(verificationCodeSchema)
    .mutation(async ({ input }) => {
      const { phone } = input;

      // 检查手机号是否已注册
      const existingUser = await prisma.user.findFirst({
        where: {
          additionalInfo: {
            phone,
          },
        },
      });

      if (existingUser) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "该手机号已注册",
        });
      }

      // 生成验证码
      const verificationCode = generateVerificationCode();

      // 存储验证码（这里使用了简单的内存存储，实际应用中应该使用Redis等）
      // 在实际应用中，应该设置过期时间
      // 这里为了简单演示，使用全局变量存储
      global.verificationCodes = global.verificationCodes || {};
      global.verificationCodes[phone] = {
        code: verificationCode,
        expireAt: Date.now() + 5 * 60 * 1000, // 5分钟过期
      };

      // 发送验证码
      const sendResult = await sendSmsVerificationCode(phone, verificationCode);

      if (!sendResult) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "验证码发送失败，请稍后重试",
        });
      }

      return {
        status: "success",
        message: "验证码已发送",
      };
    }),

  // 验证手机号和验证码（第一步注册）
  verifyPhoneCode: publicProcedure
    .input(z.object({
      phone: z.string().regex(/^1[3-9]\d{9}$/, { message: "请输入有效的手机号" }),
      verificationCode: z.string().min(4, { message: "请输入验证码" }),
    }))
    .mutation(async ({ input }) => {
      const { phone, verificationCode } = input;

      // 检查手机号是否已注册
      const existingUser = await prisma.user.findFirst({
        where: {
          additionalInfo: {
            phone,
          },
        },
      });

      if (existingUser) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "该手机号已注册",
        });
      }

      // 验证验证码
      const storedVerification = global.verificationCodes?.[phone];
      if (!storedVerification || storedVerification.code !== verificationCode || Date.now() > storedVerification.expireAt) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "验证码无效或已过期",
        });
      }

      // 标记该手机号已通过验证
      global.verifiedPhones = global.verifiedPhones || {};
      global.verifiedPhones[phone] = {
        verifiedAt: Date.now(),
        expireAt: Date.now() + 30 * 60 * 1000, // 30分钟过期
      };

      return {
        status: "success",
        message: "验证成功",
        phone: phone,
      };
    }),

  // 医生注册
  registerDoctor: publicProcedure
    .input(doctorRegisterSchema)
    .mutation(async ({ input }) => {
      const { name, hospitalName, email, phone, verificationCode, privacyAgreed, accessConditionsAgreed } = input;

      // 检查手机号是否已通过第一步验证
      const verifiedPhone = global.verifiedPhones?.[phone];
      if (!verifiedPhone || Date.now() > verifiedPhone.expireAt) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "请先完成手机号验证",
        });
      }

      // 验证验证码
      const storedVerification = global.verificationCodes?.[phone];
      if (!storedVerification || storedVerification.code !== verificationCode || Date.now() > storedVerification.expireAt) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "验证码无效或已过期",
        });
      }

      // 检查邮箱是否已注册
      const existingUserByEmail = await prisma.user.findFirst({
        where: {
          email,
        },
      });

      if (existingUserByEmail) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "该邮箱已注册",
        });
      }

      // 检查手机号是否已注册
      const existingUserByPhone = await prisma.user.findFirst({
        where: {
          additionalInfo: {
            phone,
          },
        },
      });

      if (existingUserByPhone) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "该手机号已注册",
        });
      }

      try {
        // 生成随机密码（实际应用中可能需要发送邮件通知用户）
        const password = Math.random().toString(36).slice(-8);

        // 创建用户
        const newUser = await prisma.user.create({
          data: {
            name,
            username: email, // 使用邮箱作为用户名
            email,
            password: bcrypt.hashSync(password, 10),
            usergroups: [$Enums.UserGroup.USER], // 设置为普通用户组
            additionalInfo: {
              create: {
                phone,
                department: hospitalName, // 将医院名称存储在部门字段中
              },
            },
          },
        });

        // 清除验证码和验证状态
        if (global.verificationCodes?.[phone]) {
          delete global.verificationCodes[phone];
        }

        // 清除验证状态
        if (global.verifiedPhones?.[phone]) {
          delete global.verifiedPhones[phone];
        }

        return {
          status: "success",
          message: "注册成功",
          userId: newUser.id,
        };
      } catch (error) {
        console.error("用户注册失败:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "注册失败，请稍后重试",
        });
      }
    }),
});
