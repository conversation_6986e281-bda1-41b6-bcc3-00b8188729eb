export async function sendOtp(phone: string, code: string) {
  try {
    await fetch(`${process.env.MESSAGE_TUNNEL_APP_BASE_URL}/messages/sms`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.MESSAGE_TUNNEL_APP_TOKEN}`,
      },
      body: JSON.stringify({
        message: {
          phone_number: phone,
          template_id: "SMS_484045242",
          data: {
            code: code,
          },
        },
      }),
    });
  } catch (error) {
    console.error(error);
  }
}
