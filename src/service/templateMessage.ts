

export async function sendSubscribeMessage(openid: string, templateId: string, templateParams: Record<string, string>) {
    // TODO: 发送消息提醒
    try {
        console.log("发送消息提醒", openid, templateId, templateParams);
    } catch (error) {
        console.error(error);
    }
}

export async function cancelSubscribeMessage(messageId: string) {
    // TODO: 取消消息提醒
    try {
        console.log("取消消息提醒", messageId);
    } catch (error) {
        console.error(error);
    }
}