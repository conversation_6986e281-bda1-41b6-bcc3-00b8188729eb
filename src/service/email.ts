export async function sendEmail(emailAddresses: string[], subject: string, data: Record<string, string>) {
 
    // 检查环境变量（可选，但推荐在应用启动时进行）
    if (!process.env.MESSAGE_TUNNEL_APP_BASE_URL || !process.env.MESSAGE_TUNNEL_APP_TOKEN) {
        throw new Error("错误：MESSAGE_TUNNEL_APP_BASE_URL 或 MESSAGE_TUNNEL_APP_TOKEN 环境变量未设置。");
    }

    for (const emailAddress of emailAddresses) {
        console.log(`\n[处理中] 邮件地址: ${emailAddress}`);
        try {
            await fetch(
                `${process.env.MESSAGE_TUNNEL_APP_BASE_URL}/messages/email`,
                {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${process.env.MESSAGE_TUNNEL_APP_TOKEN}`,
                    },
                    body: JSON.stringify({
                        "message": {
                            "recipient": emailAddress,
                            "template_id": "aec_appointment",
                            "data": {
                                "subject": subject, // 主题放在 data 对象中，供模板使用
                                ...data          // 其他模板变量
                            },
                            "subject": subject
                        }
                    }),
                },
            );

        } catch (error) {
            // 网络问题或其他导致 fetch 本身抛出错误的异常 (例如 DNS 解析失败、服务器不可达)
            console.log(error)
        }
    }
}

