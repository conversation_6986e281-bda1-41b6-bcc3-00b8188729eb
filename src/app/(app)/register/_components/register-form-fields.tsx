"use client";

import { type FormValues } from "../_hooks/useRegisterForm";
import AETextField from "~/components/app/common/form/ae-text-filed";
import AEButton from "~/components/app/common/ae-button";
import { Checkbox } from "~/components/ui/checkbox";
import Link from "next/link";
import { PhoneIcon, KeyIcon } from "lucide-react";
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  FormLabel,
} from "~/components/ui/form";
import { type UseFormReturn } from "react-hook-form";

interface RegisterFormFieldsProps {
  form: UseFormReturn<FormValues>;
  validationState: {
    phone: "none" | "valid" | "invalid";
    verificationCode: "none" | "valid" | "invalid";
  };
  setValidationState: React.Dispatch<
    React.SetStateAction<{
      phone: "none" | "valid" | "invalid";
      verificationCode: "none" | "valid" | "invalid";
    }>
  >;
  phoneRegex: RegExp;
  countdown: number;
  isSendingCode: boolean;
  handleSendVerificationCode: () => void;
  isSubmitting: boolean;
}

export function RegisterFormFields({
  form,
  validationState,
  setValidationState,
  phoneRegex,
  countdown,
  isSendingCode,
  handleSendVerificationCode,
  isSubmitting,
}: RegisterFormFieldsProps) {
  return (
    <div className="space-y-6 pt-4">
      {/* 手机号 */}
      <FormField
        control={form.control}
        name="phone"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <AETextField
                containerClassName="mb-4"
                id="phone"
                placeholder="请输入手机号"
                prefix={<PhoneIcon className="h-4 w-4" />}
                validationState={
                  validationState.phone === "invalid"
                    ? "invalid"
                    : form.formState.errors.phone
                      ? "invalid"
                      : phoneRegex.test(field.value)
                        ? "valid"
                        : "none"
                }
                {...field}
                onChange={(e) => {
                  field.onChange(e);
                  setValidationState((prevState) => ({
                    ...prevState,
                    phone: phoneRegex.test(e.target.value)
                      ? "valid"
                      : "none",
                  }));
                }}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* 验证码 */}
      <div className="flex items-center">
        <FormField
          control={form.control}
          name="verificationCode"
          render={({ field }) => (
            <FormItem className="flex-1">
              <FormControl>
                <AETextField
                  containerClassName="flex-1"
                  id="verificationCode"
                  placeholder="请输入验证码"
                  prefix={<KeyIcon className="h-4 w-4" />}
                  validationState={
                    validationState.verificationCode === "invalid"
                      ? "invalid"
                      : form.formState.errors.verificationCode
                        ? "invalid"
                        : field.value
                          ? "valid"
                          : "none"
                  }
                  {...field}
                  onChange={(e) => {
                    field.onChange(e);
                    setValidationState((prevState) => ({
                      ...prevState,
                      verificationCode: e.target.value ? "valid" : "none",
                    }));
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <AEButton
          className="flex-none whitespace-nowrap p-2 disabled:bg-[#BBB] disabled:text-[#FFF]"
          variant="white"
          size="sm"
          onClick={handleSendVerificationCode}
          disabled={
            isSendingCode ||
            countdown > 0 ||
            !phoneRegex.test(form.watch("phone"))
          }
        >
          {countdown > 0 ? `${countdown}秒后重试` : "获取验证码"}
        </AEButton>
      </div>


      {/* 提交按钮 */}
      <div className="flex justify-center pt-6">
        <AEButton
          className="h-[40px] w-full p-0"
          type="submit"
          disabled={isSubmitting}
        >
          {isSubmitting ? "登录中..." : "登录"}
        </AEButton>
      </div>

      {/* AEC登录链接 */}
      <div className="mt-6 flex justify-center text-sm">
        <Link href="/signin" className="text-blue-600 hover:underline font-bold">
          AEC登录
        </Link>
      </div>

      {/* 隐私协议 */}
      <FormField
        control={form.control}
        name="privacyAgreed"
        render={({ field }) => (
          <FormItem className="mt-6 flex flex-row items-center justify-center space-x-2 space-y-0">
            <FormControl>
              <Checkbox
                id="privacyAgreed"
                checked={field.value}
                onCheckedChange={field.onChange}
                className="h-5 w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </FormControl>
            <FormLabel className="text-xs text-[#9a9a9a]">
              本人已阅读并同意
              <Link
                href="/terms-conditions"
                className="text-blue-600 hover:underline"
              >
                条款条件
              </Link>
              和
              <Link href="/privacy" className="text-blue-600 hover:underline">
                隐私协议
              </Link>
            </FormLabel>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
