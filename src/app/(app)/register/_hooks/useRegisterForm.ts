import { useState, useRef } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useToast } from "~/hooks/use-toast";
import { phoneVerificationSchema } from "~/schemas/client/register";
import { api } from "~/trpc/react";
import { type z } from "zod";
import { type BaseModalRef } from "~/components/common/modals";

export type FormValues = z.infer<typeof phoneVerificationSchema>;

export function useRegisterForm() {
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSendingCode, setIsSendingCode] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [formData, setFormData] = useState<FormValues | null>(null);
  const confirmModalRef = useRef<BaseModalRef>(null);

  // 表单验证状态
  const [validationState, setValidationState] = useState({
    phone: "none" as "none" | "valid" | "invalid",
    verificationCode: "none" as "none" | "valid" | "invalid",
  });

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(phoneVerificationSchema),
    defaultValues: {
      phone: "",
      verificationCode: "",
      privacyAgreed: false,
    },
  });

  // 手机号验证正则表达式
  const phoneRegex = /^1[3-9]\d{9}$/;

  // 发送验证码API
  const sendVerificationCodeMutation =
    api.basic.sendVerificationCode.useMutation({
      onSuccess: () => {
        toast({
          title: "验证码已发送",
          description: "请查看您的手机短信",
        });
        // 开始倒计时
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      },
      onError: (error) => {
        toast({
          title: "验证码发送失败",
          description: error.message,
          variant: "destructive",
        });
        setIsSendingCode(false);
      },
    });

  // 验证手机号和验证码API
  const verifyPhoneCodeMutation = api.basic.verifyPhoneCode.useMutation({
    onSuccess: (data) => {
      toast({
        title: "验证成功",
        description: "请完善您的个人信息",
      });
      // 清除表单数据
      setFormData(null);
      setIsSubmitting(false);
      // 跳转到完善信息页面，并传递手机号参数
      router.push(`/register/complete-info?phone=${data.phone}`);
    },
    onError: (error) => {
      toast({
        title: "验证失败",
        description: error.message,
        variant: "destructive",
      });
      setIsSubmitting(false);
      // 清除表单数据
      setFormData(null);
    },
  });

  // 发送验证码
  const handleSendVerificationCode = () => {
    const phone = form.getValues("phone");
    if (!phoneRegex.test(phone)) {
      toast({
        title: "手机号格式错误",
        description: "请输入有效的手机号",
        variant: "destructive",
      });
      return;
    }

    setIsSendingCode(true);
    sendVerificationCodeMutation.mutate({ phone });
  };

  // 提交表单
  const onSubmit = (data: FormValues) => {
    // 保存表单数据
    setFormData(data);
    // 显示确认弹窗
    confirmModalRef.current?.openModal();
  };

  // 处理确认弹窗确认
  const handleConfirmModalConfirm = () => {
    if (formData) {
      setIsSubmitting(true);
      verifyPhoneCodeMutation.mutate({
        phone: formData.phone,
        verificationCode: formData.verificationCode,
      });
    }
    confirmModalRef.current?.closeModal();
  };

  // 处理确认弹窗取消
  const handleConfirmModalCancel = () => {
    // 清除表单数据
    setFormData(null);
    confirmModalRef.current?.closeModal();
  };

  return {
    form,
    validationState,
    setValidationState,
    isSubmitting,
    isSendingCode,
    countdown,
    phoneRegex,
    handleSendVerificationCode,
    onSubmit,
    confirmModalRef,
    handleConfirmModalConfirm,
    handleConfirmModalCancel,
  };
}
