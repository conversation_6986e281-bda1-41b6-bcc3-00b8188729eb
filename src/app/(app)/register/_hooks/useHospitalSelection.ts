import { useCallback, useEffect } from "react";
import { useHospitalsData, type Hospital } from "~/app/(app)/appointment/_hooks/useHospitalsData";
import { api } from "~/trpc/react";

export function useHospitalSelection() {
  // 从 useHospitalsData 中获取方法
  const [addHospitals, filterHospitalsByKeyword] = useHospitalsData(state => [
    state.addHospitals,
    state.filterHospitalsByKeyword
  ]);

  // 使用 API 查询医院数据
  const { data } = api.basic.searchHospitals.useQuery(
    { keyword: "", limit: 20, offset: 0 },
    { staleTime: 5 * 60 * 1000 } // 5分钟内不重新获取数据
  );

  // 当数据加载完成后处理
  useEffect(() => {
    if (data?.hospitals && data.hospitals.length > 0) {
      // 确保数据格式与 Hospital 类型兼容
      const hospitals = data.hospitals as unknown as Hospital[];
      addHospitals(hospitals);
    }
  }, [data, addHospitals]);

  // 根据医院ID获取医院名称
  const getHospitalNameById = useCallback((id: string): string => {
    if (!id) return "";

    // 尝试通过ID过滤医院
    const hospitals = filterHospitalsByKeyword(id);
    const hospital = hospitals.find(h => h.id.toString() === id);

    if (hospital) {
      return hospital.name;
    }

    return "";
  }, [filterHospitalsByKeyword]);

  return {
    getHospitalNameById
  };
}
