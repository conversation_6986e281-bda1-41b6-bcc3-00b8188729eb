"use client";

import { Form } from "~/components/ui/form";
import { RegisterFormFields } from "./_components/register-form-fields";
import { useRegisterForm } from "./_hooks/useRegisterForm";
import { ConfirmationModal } from "~/components/common/modals";

export default function RegisterPage() {
  const {
    form,
    validationState,
    setValidationState,
    isSubmitting,
    isSendingCode,
    countdown,
    phoneRegex,
    handleSendVerificationCode,
    onSubmit,
    confirmModalRef,
    handleConfirmModalConfirm,
    handleConfirmModalCancel,
  } = useRegisterForm();

  return (
    <div className="h-full p-4">
      <div className="mx-auto p-6 bg-white rounded-xl shadow-xl border border-gray-200">
        <div className="flex justify-between items-end mb-6">
          <span className="text-lg xs:text-xl font-bold leading-[1.2]">登陆/注册</span>
          <span className="font-semibold text-[#9a9a9a] text-sm xs:text-base">
            （仅面向医疗卫生专业人士）
          </span>
        </div>
        <div>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <RegisterFormFields
                form={form}
                validationState={validationState}
                setValidationState={setValidationState}
                phoneRegex={phoneRegex}
                countdown={countdown}
                isSendingCode={isSendingCode}
                handleSendVerificationCode={handleSendVerificationCode}
                isSubmitting={isSubmitting}
              />
            </form>
          </Form>
        </div>
      </div>

      {/* 注册确认弹窗 */}
      <ConfirmationModal
        ref={confirmModalRef}
        title="注册须知"
        content={
          <div>
            <p className="font-bold">仅面向医疗卫生专业人士</p>
            <br />
            <p className="text-xs">您正在访问爱尔康的医疗卫生专业人士在线平台。本平台旨在为教育和说明目的，仅面向持有执照的医疗卫生专业人士提供一般产品信息，而非向公众提供信息或医疗建议。网站内容仅供参考，不构成对任何产品、服务或治疗的推荐和认可。</p>
            <br />
            <p className="text-xs">如您能确认下述事项，请点击&quot;是&quot;，继续注册。</p>
            <br />
            <ul className="list-disc pl-5 text-xs space-y-2">
              <li>本人已阅读并同意上述内容。</li>
              <li>本人承诺符合访问条件，如后续不再符合访问条件，本人将停止访问并注销账号。</li>
              <li>本人已阅读并同意条款和条件以及隐私政策。</li>
            </ul>
            <br />
            <p className="text-xs">如您不能确认前述事项，请点击&quot;否&quot;，退出注册。</p>
          </div>
        }
        confirmText="是，继续注册"
        cancelText="否，退出注册"
        onConfirm={handleConfirmModalConfirm}
        onCancel={handleConfirmModalCancel}
      />
    </div>
  );
}
