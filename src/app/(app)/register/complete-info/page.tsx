"use client";

import { Form } from "~/components/ui/form";
import { CompleteInfoFormFields } from "./_components/complete-info-form-fields";
import { useCompleteInfoForm } from "./_hooks/useCompleteInfoForm";

export default function CompleteInfoPage() {
  const {
    form,
    validationState,
    setValidationState,
    isSubmitting,
    emailRegex,
    onSubmit,
  } = useCompleteInfoForm();

  return (
    <div className="h-full">
      <div className="mx-auto max-w-2xl px-4 py-8">
        <div className="flex justify-between items-end mb-6">
          <span className="text-xl font-bold">完善个人信息</span>
          <span className="font-semibold text-[#9a9a9a]">
            （仅面向医疗卫生专业人士）
          </span>
        </div>
        <div>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <CompleteInfoFormFields
                form={form}
                validationState={validationState}
                setValidationState={setValidationState}
                emailRegex={emailRegex}
                isSubmitting={isSubmitting}
              />
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}
