"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter, useSearchParams } from "next/navigation";
import { useToast } from "~/hooks/use-toast";
import { doctorRegisterSchema } from "~/schemas/client/register";
import { api } from "~/trpc/react";
import { type z } from "zod";

export type FormValues = z.infer<typeof doctorRegisterSchema>;

export function useCompleteInfoForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [phone, setPhone] = useState("");
  const [verificationCode, setVerificationCode] = useState("");

  // 表单验证状态
  const [validationState, setValidationState] = useState({
    name: "none" as "none" | "valid" | "invalid",
    hospitalName: "none" as "none" | "valid" | "invalid",
    hospitalId: "none" as "none" | "valid" | "invalid",
    email: "none" as "none" | "valid" | "invalid",
  });

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(doctorRegisterSchema),
    defaultValues: {
      name: "",
      hospitalName: "",
      hospitalId: "",
      email: "",
      phone: "",
      verificationCode: "",
      privacyAgreed: true, // 从第一步带过来的
      accessConditionsAgreed: false,
      internalAgreed: false,
    },
  });

  // 邮箱验证正则表达式
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  // 获取URL参数中的手机号
  useEffect(() => {
    const phoneParam = searchParams.get("phone");
    const codeParam = searchParams.get("code");
    
    if (!phoneParam) {
      toast({
        title: "参数错误",
        description: "请先完成手机号验证",
        variant: "destructive",
      });
      router.push("/register");
      return;
    }

    setPhone(phoneParam);
    form.setValue("phone", phoneParam);
    
    if (codeParam) {
      setVerificationCode(codeParam);
      form.setValue("verificationCode", codeParam);
    }
  }, [searchParams, form, router, toast]);

  // 注册API
  const registerMutation = api.basic.registerDoctor.useMutation({
    onSuccess: () => {
      toast({
        title: "注册成功",
        description: "您已成功注册，请使用邮箱登录",
      });
      // 延迟跳转到登录页面
      setTimeout(() => {
        router.push("/signin");
      }, 2000);
    },
    onError: (error) => {
      toast({
        title: "注册失败",
        description: error.message,
        variant: "destructive",
      });
      setIsSubmitting(false);
    },
  });

  // 提交表单
  const onSubmit = (data: FormValues) => {
    // 确保使用从URL获取的手机号和验证码
    data.phone = phone;
    data.verificationCode = verificationCode;
    
    setIsSubmitting(true);
    registerMutation.mutate(data);
  };

  return {
    form,
    validationState,
    setValidationState,
    isSubmitting,
    emailRegex,
    onSubmit,
  };
}
