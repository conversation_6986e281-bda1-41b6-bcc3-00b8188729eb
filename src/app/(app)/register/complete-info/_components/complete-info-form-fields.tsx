"use client";

import { useHospitalSelection } from "../../_hooks/useHospitalSelection";
import { type FormValues } from "../_hooks/useCompleteInfoForm";
import { SelectHospital } from "~/app/(app)/appointment/_components/select-hospital";
import AETextField from "~/components/app/common/form/ae-text-filed";
import AEButton from "~/components/app/common/ae-button";
import { Checkbox } from "~/components/ui/checkbox";
import Link from "next/link";
import {
  UserIcon,
  Building2Icon,
  MailIcon,
} from "lucide-react";
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  FormLabel,
} from "~/components/ui/form";
import { type UseFormReturn } from "react-hook-form";

interface CompleteInfoFormFieldsProps {
  form: UseFormReturn<FormValues>;
  validationState: {
    name: "none" | "valid" | "invalid";
    hospitalName: "none" | "valid" | "invalid";
    hospitalId: "none" | "valid" | "invalid";
    email: "none" | "valid" | "invalid";
  };
  setValidationState: React.Dispatch<
    React.SetStateAction<{
      name: "none" | "valid" | "invalid";
      hospitalName: "none" | "valid" | "invalid";
      hospitalId: "none" | "valid" | "invalid";
      email: "none" | "valid" | "invalid";
    }>
  >;
  emailRegex: RegExp;
  isSubmitting: boolean;
}

export function CompleteInfoFormFields({
  form,
  validationState,
  setValidationState,
  emailRegex,
  isSubmitting,
}: CompleteInfoFormFieldsProps) {
  const { getHospitalNameById } = useHospitalSelection();

  return (
    <div className="space-y-4">
      {/* 姓名 */}
      <FormField
        control={form.control}
        name="name"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <AETextField
                containerClassName="mb-4"
                id="name"
                placeholder="请输入姓名"
                prefix={<UserIcon className="h-4 w-4" />}
                validationState={
                  validationState.name === "invalid"
                    ? "invalid"
                    : form.formState.errors.name
                      ? "invalid"
                      : field.value
                        ? "valid"
                        : "none"
                }
                {...field}
                onChange={(e) => {
                  field.onChange(e);
                  setValidationState((prevState) => ({
                    ...prevState,
                    name: e.target.value ? "valid" : "none",
                  }));
                }}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* 邮箱 */}
      <FormField
        control={form.control}
        name="email"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <AETextField
                containerClassName="mb-4"
                type="email"
                id="email"
                placeholder="请输入邮箱地址"
                prefix={<MailIcon className="h-4 w-4" />}
                validationState={
                  validationState.email === "invalid"
                    ? "invalid"
                    : form.formState.errors.email
                      ? "invalid"
                      : emailRegex.test(field.value)
                        ? "valid"
                        : "none"
                }
                {...field}
                onChange={(e) => {
                  field.onChange(e);
                  setValidationState((prevState) => ({
                    ...prevState,
                    email: emailRegex.test(e.target.value)
                      ? "valid"
                      : "none",
                  }));
                }}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* 医院选择 */}
      <div className="mb-4">
        <FormField
          control={form.control}
          name="hospitalId"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <SelectHospital
                  value={field.value}
                  onChange={(hospitalId) => {
                    field.onChange(hospitalId);
                    // 当选择医院时，同时更新医院名称字段
                    if (hospitalId) {
                      const hospitalName = getHospitalNameById(hospitalId);
                      form.setValue("hospitalName", hospitalName);
                      setValidationState((prevState) => ({
                        ...prevState,
                        hospitalId: "valid",
                        hospitalName: hospitalName ? "valid" : "none",
                      }));
                    } else {
                      setValidationState((prevState) => ({
                        ...prevState,
                        hospitalId: "none",
                        hospitalName: "none",
                      }));
                    }
                  }}
                  className="px-0 text-sm text-[rgb(10, 10, 10)]"
                  prefix={<Building2Icon className="h-4 w-4" />}
                  containerClassName="mb-4"
                  validationState={
                    validationState.hospitalId === "invalid"
                      ? "invalid"
                      : form.formState.errors.hospitalId
                        ? "invalid"
                        : field.value
                          ? "valid"
                          : "none"
                  }
                  placeholder="请输入医院名称或SFID搜索"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="text-xs text-[#9a9a9a]">
        本平台旨在为教育和说明目的，仅面向持有执照的医疗卫生专业人士提供一般产品信息，而非向公众提供信息或医疗建议。本微信小程序内的内容仅供参考，不构成对任何产品、服务或治疗的推荐和认可。
      </div>

      {/* === Checkbox Section Start === */}
      {/* 1. 同意上述内容 */}
      <FormField
        control={form.control}
        name="internalAgreed"
        render={({ field }) => (
          <FormItem className="mt-4 flex flex-row items-center space-x-2 space-y-0">
            <FormControl>
              <Checkbox
                id="internalAgreed"
                checked={field.value}
                onCheckedChange={field.onChange}
                className="h-5 w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </FormControl>
            <FormLabel className="text-xs text-[#9a9a9a]">
              本人已阅读并同意上述内容。
            </FormLabel>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* 2. 承诺访问条件 */}
      <FormField
        control={form.control}
        name="accessConditionsAgreed"
        render={({ field }) => (
          <FormItem className="mt-4 flex flex-row items-start space-x-2 space-y-0">
            <FormControl>
              <Checkbox
                id="accessConditionsAgreed"
                checked={field.value}
                onCheckedChange={field.onChange}
                className="h-5 w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </FormControl>
            <FormLabel className="text-xs text-[#9a9a9a]">
              本人承诺符合访问条件，如后续不再符合访问条件，本人将停止访问并注销账号。
            </FormLabel>
            <FormMessage />
          </FormItem>
        )}
      />
      {/* === Checkbox Section End === */}

      {/* 提交按钮 */}
      <div className="flex justify-center pt-6">
        <AEButton
          className="h-[40px] w-[140px] p-0"
          type="submit"
          disabled={isSubmitting}
        >
          {isSubmitting ? "注册中..." : "完成"}
        </AEButton>
      </div>
    </div>
  );
}
