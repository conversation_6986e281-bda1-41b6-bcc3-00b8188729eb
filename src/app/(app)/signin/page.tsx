"use client";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useToast } from "~/hooks/use-toast";
import LoginBanner from "~/components/app/login/banner";
import LoginForm from "~/components/app/login/form";
import { generateReloadUrl } from "~/lib/url";
import useModalStore from "~/hooks/useModalStore";
import { $Enums } from "@prisma/client";
import { Modal } from "~/components/common/modal";
import React from "react";

export default function SigninPage({ searchParams }: { searchParams: Promise<Record<string, string | string[]>> }) {
  const router = useRouter();
  const { toast } = useToast();
  const { showModal } = useModalStore();

  const handleSubmit = async (data: { email: string; password: string }, callbackUrl: string) => {
    // 调用登录API
    const result = await signIn("credentials", {
      redirect: false,
      username: data.email,
      password: data.password,
      callbackUrl,
    });

    if (result?.error) {
      toast({
        title: "登录失败",
        description: "请检查邮箱和密码是否正确",
      });
      return;
    }

    // 登录成功
    toast({
      title: "登录成功",
      description: "欢迎回来！",
    });

    // 获取用户信息，包括用户组
    try {
      // 从JWT令牌中获取用户组信息
      const userResponse = await fetch("/api/auth/session");
      const userData = await userResponse.json();
      const userGroups = userData.user?.usergroups ?? [];

      // 检查用户组
      if (userGroups.includes($Enums.UserGroup.USER)) {
        // 医生用户，显示提示信息
        showModal(
          "提示",
          <div className="py-4 text-center">
            <p>如需预约参观AEC体验中心，请联系您身边的爱尔康销售代表</p>
          </div>
        );
        // 不进行跳转，停留在当前页面
        return;
      }

      // 销售或培训人员，正常跳转
      if (userGroups.includes($Enums.UserGroup.SALES) || userGroups.includes($Enums.UserGroup.TRAINER)) {
        // 优先使用NextAuth返回的url
        if (result?.url) {
          router.push(result.url);
        }
        // 如果是默认个人中心且有历史记录，则返回上一页
        else if (callbackUrl === "/profile" && typeof window !== "undefined" && window.history.length > 1) {
          router.back();
        }
        // 否则使用自定义callbackUrl
        else {
          const url = generateReloadUrl(callbackUrl);
          router.push(url);
        }
      } else {
        // 其他用户组，跳转到首页
        router.push("/");
      }
    } catch (error) {
      console.error("获取用户信息失败", error);
      // 出错时默认跳转
      router.push(callbackUrl);
    }
  };

  // 使用 React.use() 解包 searchParams
  const unwrappedSearchParams = React.use(searchParams);

  return (
    <div className="h-full">
      <div className="max-w-2xl mx-auto">
        <LoginBanner className="mb-8" />
        <LoginForm searchParams={unwrappedSearchParams} onSubmit={handleSubmit} />
        {/* 确保Modal组件可用 */}
        <Modal />
      </div>
    </div>
  );
}