"use client";

import { useRouter } from "next/navigation";
import ProfileCard from "~/components/app/profile/common/profile-card";
import { api } from "~/trpc/react";
import { ERCode } from "~/components/app/common/er-code";
import Loading from "~/components/app/common/loading";
import AppointmentList from "~/components/app/profile/appointment/appointment-list";
import AEButton from "~/components/app/common/ae-button";
import { LockKeyhole } from "lucide-react";

export default function ProfilePage() {
  const router = useRouter();
  // Fetch profile data
  const { data: profileData, isLoading } = api.basic.getProfile.useQuery();

  // 处理加载状态
  if (isLoading) {
    return <Loading />;
  }

  // 跳转到修改密码页面
  const handleChangePassword = () => {
    router.push("/profile/password");
  };

  return (
    <div className="flex flex-col">
      {/* <AeHeader /> */}
      <div className="mx-auto w-full max-w-2xl px-4 pb-12">
        <ProfileCard
          name={profileData?.name}
          employeeId={profileData?.employeeId}
          groups={profileData?.userGroups}
          avatar={profileData?.avatar}
          editable={false}
        />
        <div className="mt-6 justify-center hidden">
          <AEButton
            onClick={handleChangePassword}
            className="flex items-center gap-2"
          >
            <LockKeyhole className="h-4 w-4" />
            修改密码
          </AEButton>
        </div>
        <AppointmentList />
        <ERCode className="my-8" />
      </div>
    </div>
  );
}
