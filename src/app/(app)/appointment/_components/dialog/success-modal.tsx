"use client";
import { SuccessModal as NewSuccessModal } from "~/components/common/modals";
import "./dialog.css";

interface SuccessModalProps {
  opened: boolean;
  setOpened: (open: boolean) => void;
  onConfirm?: () => void;
}

const SuccessModal = ({ opened, setOpened, onConfirm }: SuccessModalProps) => {
  const handleClose = () => {
    setOpened(false);
  };

  return (
    <NewSuccessModal
      initiallyOpened={opened}
      onClose={handleClose}
      onConfirm={onConfirm}
      confirmText="我已知晓"
      preventOutsideClose={true}
    />
  );
};

SuccessModal.displayName = "SuccessModal";

export default SuccessModal;
