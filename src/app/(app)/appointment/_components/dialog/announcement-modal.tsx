"use client";

import { AnnouncementModal as NewAnnouncementModal } from "~/components/common/modals";

interface AnnouncementModalProps {
  show: boolean;
  onConfirm: () => void;
}

const announcement = (
  <div className="text-[#6c6c6c] max-h-[280px] overflow-y-auto">
    <p className="mb-3">
      请注意预约外部客户到访AEC体验活动，应在客户到访AEC活动前完成公司有关合规审批手续;
    </p>
    <p className="mb-3">
      <strong>预约</strong>HCP动物眼手术练习请安排<strong>周一和周三</strong>
    </p>
    <p className="mb-3">
      AEC原则上周末不对外开放，如需申请活动请至少<strong>提前7天联系AEC</strong>
    </p>
    <p className="mb-3">
      电话：13521823123
    </p>
  </div>
);

export const AnnouncementModal = ({
  show,
  onConfirm,
}: AnnouncementModalProps) => {
  return (
    <NewAnnouncementModal
      title="预约公告"
      content={announcement}
      initiallyOpened={show}
      onConfirm={onConfirm}
      confirmText="我已知晓"
    />
  );
};
