"use client";

import { useState, useEffect } from "react";
import { NoticeModal as NewNoticeModal } from "~/components/common/modals";

export interface NoticeModalProps {
  show: boolean;
  content: string;
  onBack: () => void;
  onConfirm: () => void;
  projectId?: number; // 设为可选参数
}

export function NoticeModal({ show, content, onConfirm, onBack }: NoticeModalProps) {
  return (
    <NewNoticeModal
      title="预约须知"
      content={<div dangerouslySetInnerHTML={{ __html: content ?? "" }} />}
      initiallyOpened={show}
      onConfirm={onConfirm}
      onCancel={onBack}
      confirmText="我已知晓"
      cancelText="返回上一层"
    />
  );
}

// 自定义 Hook: 处理预约须知模态框
export const useNoticeModal = () => {
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    const hasSeenModal = sessionStorage.getItem(`hasSeenModalNotice`);
    if (!hasSeenModal) {
      setShowModal(true);
    }
  }, []);

  const handleModalConfirm = () => {
    sessionStorage.setItem(`hasSeenModalNotice`, "true");
    setShowModal(false);
  };

  return { showModal, handleModalConfirm };
};
