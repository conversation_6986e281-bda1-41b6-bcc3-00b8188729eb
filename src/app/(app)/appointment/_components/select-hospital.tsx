"use client"

import * as React from "react"
import { Input } from "~/components/ui/input"
import { ScrollArea } from "~/components/ui/scroll-area"
import { Separator } from "~/components/ui/separator"
import { api } from "~/trpc/react"

import { Search } from "lucide-react"
import { Button } from "~/components/ui/button"
import Spinner from "./spinner"
import { cn } from "~/lib/utils"
import { debounce } from "lodash"
import { useHospitalsData, type Hospital } from "../_hooks/useHospitalsData"
import { AeCheckIcon, AeCloseIcon } from "~/components/app/common/icons"

interface SelectHospitalProps {
    value: string;
    onChange: (hospitalId: string) => void;
    prefix?: React.ReactNode;
    suffix?: React.ReactNode;
    validationState?: "valid" | "invalid" | "none";
    containerClassName?: string;
    variant?: "default" | "rounded";
    placeholder?: string;
    className?: string;
    // 添加更多可配置的属性
    width?: string;
    height?: string;
    fontSize?: string;
    disabled?: boolean;
}

export function SelectHospital({
    value,
    onChange,
    prefix,
    suffix,
    validationState = "none",
    containerClassName,
    variant = "default",
    placeholder = "输入医院名称 或 SFID 搜索",
    className,
    width,
    height,
    fontSize,
    disabled = false
}: SelectHospitalProps) {
    const [keywords, addKeyword, addHospitals, filterHospitalsByKeyword] = useHospitalsData(state=>[
        state.keywords,
        state.addKeyword,
        state.addHospitals,
        state.filterHospitalsByKeyword
    ]);
    const [list, setList] = React.useState<Hospital[]>([])
    const [open, setOpen] = React.useState(false)
    const [isComposing, setIsComposing] = React.useState(false)
    const [searchInputValue, setSearchInputValue] = React.useState("")
    const [searchValue, setSearchValue] = React.useState("")

    const { data, isLoading, isFetching } = api.basic.searchHospitals.useQuery(
        {
            keyword: searchValue,
            limit: 20,
            offset: 0
        },
        {
            enabled: searchValue.length > 0,
            staleTime: 5 * 60 * 1000 // 5分钟内不重新获取数据
        }
    );

    React.useEffect(()=>{
        if( data?.hospitals && data?.hospitals.length > 0){
            addHospitals(data?.hospitals || [])
        }
        if (searchInputValue.length > 0){
            const valuesFromStore = filterHospitalsByKeyword(searchInputValue)
            if (valuesFromStore.length > 0){
                setList(valuesFromStore)
                return;
            }
        }
        setList([])
    },[data, filterHospitalsByKeyword,searchInputValue, searchValue])


    const performSearch = React.useCallback((val: string)=>{
        if (keywords.has(val)){
            return
        }
        addKeyword(val)
        setSearchValue(val)
    },[keywords])

    const performValidation = React.useCallback((val: string)=>{
        const valuesFromStore = filterHospitalsByKeyword(val)
        if (valuesFromStore.length === 1 && (
            valuesFromStore[0]!.id.toString() === val ||
            valuesFromStore[0]!.name === val
        )){
            onChange(valuesFromStore[0]!.id.toString())
        } else {
            onChange("")
        }
    },[performSearch, onChange])

    const onClickHospital = React.useCallback((hospital: Hospital)=>{
        setSearchInputValue(hospital.name)
        onChange(hospital.id.toString())
    },[onChange])

    // 变体样式定义
    const variantStyles = {
        default: "border-b px-2",
        rounded: "rounded-full shadow-lg bg-white px-4",
    };

    // 自定义样式
    const customStyles = {
        width: width || "100%",
        height: height,
        fontSize: fontSize,
    };

    return (
        <div
            className={cn(
                `flex items-center text-sm ${variantStyles[variant]} ${
                    validationState === "invalid"
                        ? "border-red-500"
                        : "border-gray-300 focus-within:border-[#0F40F5]"
                }`,
                containerClassName
            )}
            style={customStyles}
        >
            {prefix && <span className="mr-2">{prefix}</span>}
            <Input
                value={searchInputValue}
                onChange={e=>{
                    const val = e.target.value
                    setSearchInputValue(val)
                }}
                onCompositionStart={()=>{
                    setIsComposing(true)
                }}
                onCompositionEnd={()=>{
                    setIsComposing(false)
                    performSearch(searchInputValue)
                    performValidation(searchInputValue)
                }}
                onInput={e=>{
                    const val = (e.target as HTMLInputElement).value
                    setSearchInputValue(val)
                    if(!isComposing){
                        performSearch(val)
                        performValidation(val)
                    }
                }}
                placeholder={placeholder}
                className={cn(
                    "flex-1 bg-transparent py-2 !border-none !shadow-none !outline-none focus-visible:ring-transparent",
                    className
                )}
                style={{ fontSize: fontSize }}
                onFocus={() => {
                    setOpen(true)
                }}
                onBlur={() => {
                    setTimeout(()=>setOpen(false), 200)
                }}
                disabled={disabled}
            />
            {validationState === "valid" && (
                <AeCheckIcon className="text-green-500 mr-0.5" />
            )}
            {validationState === "invalid" && (
                <AeCloseIcon className="text-red-500 mr-0.5" />
            )}
            {suffix && <span className="ml-2">{suffix}</span>}

            <div className={
                cn("absolute left-0 right-0 w-full h-0 z-20" ,{
                    ["hidden"]: !open
                })
            }>
                <div className="absolute top-8 left-0 right-0 w-full flex flex-col bg-gray-50 shadow-lg rounded-lg">
                    <div className="p-2 flex-1">
                        {
                            isLoading ? (
                                <div className="flex items-center justify-center h-full p-4">
                                    <Spinner />
                                </div>
                            ) : (
                                <>
                                    {
                                        list.length > 0 ? (
                                            <ScrollArea className="h-[400px] w-full bg-white">
                                                <div className="px-4 pt-4">
                                                    {list.map((hospital) => (
                                                        <div key={hospital.id}>
                                                            <button
                                                                type="button"
                                                                className="text-sm cursor-pointer text-left"
                                                                onClick={(e)=>{
                                                                    onClickHospital(hospital)
                                                                }}
                                                            >
                                                                {hospital.name} - ({hospital.sfid})
                                                            </button>
                                                            <Separator className="my-2" />
                                                        </div>
                                                    ))}
                                                </div>
                                            </ScrollArea>
                                        ) : (
                                            <div className="flex items-center justify-center h-full">
                                                {
                                                    searchInputValue === "" ? (
                                                        <p className="text-sm text-gray-500">输入关键字查询后从列表中选择医院</p>
                                                    ) :(
                                                        <p className="text-sm text-gray-500">没有找到相关医院</p>
                                                    )
                                                }
                                            </div>
                                        )
                                    }
                                </>
                            )
                        }
                    </div>
                </div>
            </div>
        </div>
    )
}
