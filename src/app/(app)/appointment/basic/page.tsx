/* eslint-disable react-hooks/exhaustive-deps */
"use client";
import {
  AimIcon,
  LocateIcon,
  OperationIcon,
} from "~/components/app/common/icons";
import Selector from "../_components/slector";
import { $Enums } from "@prisma/client";
import { useAppointmentFormStore } from "../_hooks/useAppointmentFormStore";
import { api } from "~/trpc/react";
import React from "react";
import Spinner from "../_components/spinner";
import { motion } from "framer-motion";
import { cn } from "~/lib/utils";
import StepButtons from "../_components/step-buttons";
import { Separator } from "~/components/ui/separator";
import { AnnouncementModal } from "../_components/dialog/announcement-modal";
import { useToast } from "~/hooks/use-toast";
import { useRouter } from 'next/navigation';
import { useModalStore } from "../_hooks/useModalStore";

export default function BasicPage() {
  const router = useRouter();
  const { toast , dismiss} = useToast();
  const { data: locations = [], isLoading: isLocationsLoading } =
    api.basic.getLocations.useQuery();
  const { data: allExtraOptions, isLoading: isExtraOptionsLoading } =
    api.basic.getExtraOptions.useQuery();
  const [basic, updateBasic] = useAppointmentFormStore((state) => [
    state.basic,
    state.updateBasic,
  ]);

  // 修复：使用函数而不是值
  const modalStore = useModalStore();
  const isGlobalModalChecked = modalStore.isGlobalModalChecked();
  const setGlobalModalChecked = modalStore.setGlobalModalChecked;

  // 获取项目列表
  const { data: projectIdData, isLoading , refetch  } = api.basic.project.getProjectId.useQuery(
    {
      locationId: basic.locationId,
      type: basic.projectType,
      optionId: basic.optionId
    },
    {
      enabled:  false,
    }
  );


  // const { isValid, isNavigating, navigateToNext } = useBasicFormValidation();

  const locationsSelectorData = React.useMemo(() => {
    return locations.map((item) => ({ key: item.id, label: item.name }));
  }, [locations]);

  const optionsSelectorData = React.useMemo(() => {
    if (!allExtraOptions) return [];

    const filteredOptions = basic.projectType
      ? allExtraOptions.filter((item) => item.type === basic.projectType)
      : allExtraOptions;

    return filteredOptions.map(({ id, name }) => ({ key: id, label: name }));
  }, [allExtraOptions, basic.projectType]);

  const nextDisabled = React.useMemo(() => {
    return !basic.locationId || !basic.projectType || (optionsSelectorData.length !== 0 && !basic.optionId)
  }, [basic, optionsSelectorData])

  React.useEffect(()=>{
    dismiss();
  },[])

  React.useEffect(() => {
    updateBasic({
      locationId: locations[0]?.id,
      projectType: $Enums.AppointmentProjectType.PROCEDURE,
      optionId: undefined,
      projectId: undefined,
    });
  }, [locations, updateBasic]);

  React.useEffect(() => {
    updateBasic({
      ...basic,
      optionId: undefined,
      projectId: undefined, // 重置projectId
    });
  }, [basic.projectType]);


  const handleClickNext = async () => {
    const {data} = await refetch();
    if (data){

      const projectId = data.id;
      updateBasic({
        ...basic,
        projectId: projectId
      })
      router.push('/appointment/time');
    } else {
      toast({
        title: "错误",
        description: "没有可预约项目",
        variant: "destructive",
        duration: 3000,
      });
    }
  }

  return (
    <main>
      <AnnouncementModal
        show={!isGlobalModalChecked}
        onConfirm={() => {
          console.log("Modal confirmed, setting global modal checked");
          setGlobalModalChecked();
        }}
      />
      {!isLocationsLoading && !isExtraOptionsLoading ? (
        <motion.div
          initial="hidden"
          animate="visible"
          variants={{
            visible: {
              transition: { staggerChildren: 0.3 }, // Staggers each child by 0.2s
            },
          }}
          className="w-full"
        >
          <motion.div
            variants={{
              hidden: { opacity: 0, scale: 0.9 },
              visible: { opacity: 1, scale: 1 },
            }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="w-full"
          >
            <Selector
              renderIcon={() => (
                <LocateIcon className="mr-[9px] h-[18px] w-[18px]" />
              )}
              title="预约地点"
              options={locationsSelectorData}
              activeItem={basic.locationId}
              onSelectItem={(val) => {
                updateBasic({
                  ...basic,
                  locationId: val,
                });
              }}
            />
          </motion.div>
          <motion.div
            variants={{
              hidden: { opacity: 0, scale: 0.9 },
              visible: { opacity: 1, scale: 1 },
            }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="w-full"
          >
            <Selector
              renderIcon={() => (
                <AimIcon className="mr-[9px] h-[18px] w-[18px]" />
              )}
              title="预约目的"
              options={[
                {
                  key: $Enums.AppointmentProjectType.PROCEDURE,
                  label: "实验",
                },
                {
                  key: $Enums.AppointmentProjectType.TOUR,
                  label: "参观",
                },
                {
                  key: $Enums.AppointmentProjectType.LIVE,
                  label: "直播",
                },
                {
                  key: $Enums.AppointmentProjectType.TRAINING,
                  label: "培训",
                },
              ]}
              activeItem={basic.projectType}
              onSelectItem={(val) => {
                updateBasic({
                  ...basic,
                  projectType: val,
                });
              }}
            />
          </motion.div>
          <motion.div
            key={optionsSelectorData.length}
            variants={{
              hidden: { opacity: 0, scale: 0.9 },
              visible: { opacity: 1, scale: 1 },
            }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className={cn("w-full", {
              hidden: optionsSelectorData.length === 0,
            })}
          >
            <Selector
              variant="flex"
              renderIcon={() => (
                <OperationIcon className="mr-[9px] h-[18px] w-[18px]" />
              )}
              title="预约术式"
              options={optionsSelectorData}
              activeItem={basic.optionId}
              onSelectItem={(val) => {
                updateBasic({
                  ...basic,
                  optionId: val,
                });
              }}
            />
          </motion.div>
          <motion.div
            variants={{
              hidden: { opacity: 0, scale: 0.9 },
              visible: { opacity: 1, scale: 1 },
            }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="w-full"
          >
            <Separator className="color-[rgba(255,255,255,1)]" />
            <StepButtons
              onNext={handleClickNext}
              nextDisabled={nextDisabled}
              nextLoading={isLoading}
              onPrev={() => {
                router.replace('/');
              }}
            />
          </motion.div>
        </motion.div>
      ) : (
        <div className="p-8">
          <Spinner />
        </div>
      )}
      <div className="w-full min-h-[100px]"></div>
    </main>
  );
}
