"use client";
import Link from "next/link";
import Form1 from "../_components/form/form1";

import { useRouter } from "next/navigation";
import React, { useState, useEffect } from "react";
import { useAppointmentFormStore } from "../_hooks/useAppointmentFormStore";
import { NoticeModal } from "../_components/dialog/notice-modal";
import SuccessModal from "../_components/dialog/success-modal";
import { api } from "~/trpc/react";
import Spinner from "../_components/spinner";
import { useModalStore } from "../_hooks/useModalStore";
import { useToast } from "~/hooks/use-toast";
import { type z } from "zod";
import { type createAppointmentSchema } from "~/schemas/client/appointment";
import { ChevronLeft } from "lucide-react";

export default function Page() {
  const { toast } = useToast();
  const router = useRouter();
  const [isSubmitSuccessModalOpen, setIsSubmitSuccessModalOpen] = useState(false);
  const [basic, time, resetForm] = useAppointmentFormStore((state) => [state.basic, state.time, state.resetForm]);
  // 使用 modalStore 来管理预约须知的显示状态
  const { clearProjectModalChecked, setProjectModalChecked } = useModalStore();

  // 在组件挂载时清除项目的确认状态，确保每次预约都会显示预约须知
  useEffect(() => {
    if (basic.projectId) {
      clearProjectModalChecked(basic.projectId);
    }
  }, [basic.projectId, clearProjectModalChecked]);
  const { data: projectDetailData, isLoading: isProjectDetailLoading } = api.basic.project.getProjectDetail.useQuery({ id: basic.projectId });
  const { mutateAsync: createAppointmentAsync } = api.basic.project.createBooking.useMutation();

  const hasTextContent = React.useMemo(() => {
    if (!projectDetailData?.description) {
      return false;
    }
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = projectDetailData?.description;
    const text = tempDiv.textContent?.trim();
    return text && text.length > 0;
  }, [projectDetailData?.description]);



  const handleSubmit = async (values: z.infer<ReturnType<typeof createAppointmentSchema>>) => {
    if (!time.timeSlot) {
      toast({
        title: "请选择时间",
        description: "请回到上一步选择预约时间段",
      });
      return;
    }

    if (!basic.projectId) {
      toast({
        title: "请选择预约项目",
        description: "请回到第一步选择预约项目",
      });
      return;
    }
    const res = await createAppointmentAsync({
      ...values,
      projectId: basic.projectId,
      startTime: time.timeSlot?.startTime.toISOString(),
      endTime: time.timeSlot?.endTime.toISOString(),
    });
    if (res.id) {
      setIsSubmitSuccessModalOpen(true);
      resetForm();
    }
  };



  if (isProjectDetailLoading) {
    return (
      <div className="flex justify-center items-center ">
        <Spinner />
      </div>
    );
  }

  return (
    <main className="md:-mt-10">
      <div className="flex items-center gap-2">
        <Link href="/appointment/time">
          <span className="flex items-center gap-2 text-sm font-bold hover:text-primary">
            <ChevronLeft className="w-4 h-4" />
            <span>上一步</span>
          </span>
        </Link>

      </div>
      <Form1
        title={projectDetailData?.name ?? ""}
        appointmentType={
          basic.projectType
        }
        onSubmit={handleSubmit}

      />

      {/* 预约须知弹窗 */}
      {hasTextContent && (
        <NoticeModal
          show={true} // 始终显示预约须知
          onBack={() => {
            console.log("Notice modal back clicked");
            router.back();
          }}
          onConfirm={() => {
            console.log("Notice modal confirm clicked");
            // 设置项目已确认，但在下次预约时会被 useEffect 清除
            setProjectModalChecked(basic.projectId);
          }}
          content={projectDetailData?.description ?? ""}
        />
      )}
      {/* 预约成功弹窗 */}
      <SuccessModal
        opened={isSubmitSuccessModalOpen}
        setOpened={setIsSubmitSuccessModalOpen}
        onConfirm={() => {
          router.push('/profile');
        }}
      />
    </main>
  );
}
