"use client";
import { DateLocalIcon, WarningIcon } from "~/components/app/common/icons";
import Title from "../_components/title";
import StepButtons from "../_components/step-buttons";
import { Calendar } from "../_components/calendar/calendar-container";
import React from "react";
import { zhCN } from "date-fns/locale";
import { addDays, format, isBefore, isEqual } from "date-fns";
import TimeBlock, {
  type TimeBlockProps,
  TimeBlockStatus,
} from "../_components/timeBlock";
import { motion } from "framer-motion";
import { isNumber  } from "lodash";
import Spinner from "../_components/spinner";
import { useAppointmentFormStore } from "../_hooks/useAppointmentFormStore";
import { api } from "~/trpc/react";
import { useRouter } from "next/navigation";
import { useToast } from "~/hooks/use-toast";
import { useSession } from "next-auth/react";
import { $Enums } from "@prisma/client";
import { DateRange } from "react-day-picker";

type RequiredTimeBlockProps = Pick<
  TimeBlockProps,
  "startTime" | "endTime" | "status" | "remainingCapacity"
>;

 
export default function TimePage() {
  const session = useSession();
  const { toast } = useToast();
  const router = useRouter();
  const [animationKey, setAnimationKey] = React.useState(0);
  const [projectId, time, updateTime] = useAppointmentFormStore((state) => [
    state.basic.projectId,
    state.time,
    state.updateTime,
  ]);

 
  // const [date, setDate] = React.useState<Date | undefined>( new Date());
  const [date, setDate] = React.useState<DateRange | undefined>({
    from: new Date(),
    to: new Date(), // Initially set to same date
  })
  
  // Single date state for non-trainers
  const [singleDate, setSingleDate] = React.useState<Date | undefined>(new Date());

  const { data, isLoading } = api.basic.getTimeSlots.useQuery(
    {
      projectId: projectId,
      startDate: date?.from ? format(date.from, "yyyy-MM-dd") : "",
      endDate: date?.to ? format(date.to, "yyyy-MM-dd") : "",
    },
    {
      enabled: !!projectId && !!date?.from && !!date?.to,
      retry: 1,
      retryDelay: 1000,
      // 数据过期时间 (毫秒)
      staleTime: 60 * 1000, // 1分钟
    },
  );


  const isTrainer = React.useMemo(()=>{
    return session.data?.user.usergroups?.includes($Enums.UserGroup.TRAINER);
  },[session.data?.user.usergroups])

  // Update date range based on trainer status
  React.useEffect(() => {
    const today = new Date();
    if (isTrainer) {
      // Trainers can select date ranges (from today to tomorrow)
      setDate({
        from: today,
        to: today,
      });
    } else {
      // Non-trainers have same start and end date
      setSingleDate(today);
      setDate({
        from: today,
        to: today,
      });
    }
  }, [isTrainer]);

  const timeBlocksData = React.useMemo<RequiredTimeBlockProps[]>(() => {
    const now = Date();
    const timeSlots = data?.timeSlots;

    if (!timeSlots) {
      return [];
    }

    return timeSlots.filter((item) => !item.remainingCapacity || (isNumber(item.remainingCapacity) && item.remainingCapacity > 0))
      .map((item) => {
        let status = TimeBlockStatus.Available;
        if (time.timeSlot && isEqual(time.timeSlot.startTime, item.startTime ) && isEqual(time.timeSlot.endTime, item.endTime)  ){
          status = TimeBlockStatus.Selected;
        }

        // 2 expired
        if (isBefore(item.startTime, now)){
          status = TimeBlockStatus.Expired;
        }
        
        return {
          startTime: item.startTime,
          endTime: item.endTime,
          remainingCapacity: item.remainingCapacity,
          status: status,
        };
      });
  }, [data?.timeSlots, time.timeSlot]);

  React.useEffect(() => {
    // Change the key to force Framer Motion to restart the animation
    setAnimationKey((prevKey) => prevKey + 1);
  }, [data?.timeSlots]);

  React.useEffect(() => {
      updateTime({
        timeSlot: undefined
      })
  }, [updateTime])


 
  const maxDaysFromToday = React.useMemo(()=>{
    return isTrainer ? 90: 30;
  },[isTrainer])

  const handleTimeBlockClick = (start: Date, end: Date) => {
    if (start === time.timeSlot?.startTime && end === time.timeSlot?.endTime) {
      updateTime({
        timeSlot: undefined
      })
      return;
    }

    updateTime({
      timeSlot: {startTime: start, endTime: end}
    })
  };

  // Handler for single date selection (non-trainers)
  const handleSingleDateSelect = (selectedDate: Date | undefined) => {
    setSingleDate(selectedDate);
    if (selectedDate) {
      setDate({
        from: selectedDate,
        to: selectedDate,
      });
    }
    updateTime({
      timeSlot: undefined
    });
  };

  // Handler for range date selection (trainers)
  const handleRangeDateSelect = (selectedRange: DateRange | undefined) => {
    setDate(selectedRange);
    updateTime({
      timeSlot: undefined
    });
  };

  return (
    <main>
      <Title
        renderIcon={() => (
          <DateLocalIcon className="mr-[9px] h-[18px] w-[18px]" />
        )}
        title="请选择预约时间"
      />
      {isTrainer ? (
        <Calendar
          locale={zhCN}
          mode="range"
          defaultMonth={date?.from}
          selected={date}
          onSelect={handleRangeDateSelect}
          className="flex w-full justify-center"
          weekStartsOn={0}
          maxDaysFromToday={maxDaysFromToday}
        />
      ) : (
        <Calendar
          locale={zhCN}
          mode="single"
          defaultMonth={singleDate}
          selected={singleDate}
          onSelect={handleSingleDateSelect}
          className="flex w-full justify-center"
          weekStartsOn={0}
          maxDaysFromToday={maxDaysFromToday}
        />
      )}
      <div className="pb-[25px] pt-[10px]">
        <div className="flex h-[25px] w-full flex-row items-center justify-start gap-2 rounded-full border px-[6px]">
          <WarningIcon className="h-4 w-4" fill="#0F40F5" />
          <span className="text-[12px]  leading-none text-[rgba(108,108,108,1)]">
            您只能预约<b className="font-bold">{maxDaysFromToday}</b>天内的时间
          </span>
        </div>
      </div>

      {isLoading ? (
        <div className="flex h-full w-full items-center justify-center">
          <Spinner />
        </div>
      ) : (
        <>
          {timeBlocksData.length === 0 ? (
            <div className="flex h-full w-full items-center justify-center">
              <span className="text-[16px] text-[#999999]">无可用时间段</span>
            </div>
          ) : (
            <motion.div
              key={animationKey}
              initial="hidden"
              animate="visible"
              variants={{
                hidden: { opacity: 1 },
                visible: {
                  opacity: 1,
                  transition: { staggerChildren: 0.3 },
                },
              }}
              className="grid auto-rows-fr grid-cols-1 items-stretch gap-4 md:grid-cols-2"
            >
              {timeBlocksData.map((item) => (
                <motion.div
                  key={`${item.startTime.toISOString()}-${item.endTime.toISOString()}`}
                  // exit={{ opacity: 0, scale: 0.5 }}
                  variants={{
                    hidden: { opacity: 0,  scale: 0.7 },
                    visible: { opacity: 1,  scale: 1},
                  }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                  className={`min-h-full w-full`}
                >
                  <TimeBlock
                    startTime={item.startTime}
                    endTime={item.endTime}
                    status={item.status}
                    remainingCapacity={item.remainingCapacity}
                    className="min-h-full w-full"
                    onClick={() => handleTimeBlockClick(item.startTime, item.endTime)}
                  />
                </motion.div>
              ))}
            </motion.div>
          )}
        </>
      )}
      <StepButtons
        onPrev={() => router.replace("/appointment/basic")}
        onNext={() => {
          if (!time.timeSlot) {
            toast({
              title: "请选择预约时间",
              description: "请选择一个可用的时间段",
              variant: "destructive",
            });
            return;
          }

          router.push("/appointment/info");
        }}
        nextDisabled={!time.timeSlot}
      />
      <div className="min-h-[100px] w-full"></div>
    </main>
  );
}
