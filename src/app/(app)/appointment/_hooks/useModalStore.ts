/* eslint-disable @typescript-eslint/no-empty-function */
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";

// 检查是否在浏览器环境中
const isBrowser = typeof window !== 'undefined';

type ModalState = {
    global: boolean;
    projectIds: number[];
    setProjectModalChecked: (id: number) => void;
    isProjectModalChecked: (id: number) => boolean;
    clearProjectModalChecked: (id: number) => void; // 添加清除特定项目确认状态的方法
    setGlobalModalChecked: () => void;
    isGlobalModalChecked: () => boolean;
}

export const useModalStore = create<ModalState>()(
    persist(
        immer((set, get) => ({
            global: false,
            projectIds: [],
            setProjectModalChecked: (id: number) => set((state) => {
                if (!state.projectIds.includes(id)) {
                    state.projectIds.push(id);
                }
            }),
            isProjectModalChecked: (id: number) => {
                if (Array.isArray(get().projectIds)) {
                    return get().projectIds.includes(id);
                }
                return false;
            },
            clearProjectModalChecked: (id: number) => set((state) => {
                state.projectIds = state.projectIds.filter(projectId => projectId !== id);
            }),
            setGlobalModalChecked: () => {
                set({ global: true });
                // 只在浏览器环境中设置 sessionStorage
                if (isBrowser) {
                    try {
                        sessionStorage.setItem("hasSeenModalAnnouncement", "true");
                    } catch (e) {
                        console.error("Failed to set sessionStorage:", e);
                    }
                }
            },
            isGlobalModalChecked: () => {
                // 检查 zustand 状态
                const fromState = get().global;

                // 只在浏览器环境中检查 sessionStorage
                let fromSession = false;
                if (isBrowser) {
                    try {
                        fromSession = sessionStorage.getItem("hasSeenModalAnnouncement") === "true";
                    } catch (e) {
                        console.error("Failed to get from sessionStorage:", e);
                    }
                }

                console.log("Global modal checked:", fromState, "Session storage:", fromSession);
                return fromState || fromSession;
            },
        })),
        {
            name: "alcon-aea-appointment-modal-state",
            storage: createJSONStorage(() => {
                // 只在浏览器环境中使用 sessionStorage
                if (isBrowser) {
                    return sessionStorage;
                }
                // 在服务器端提供一个空的存储实现
                return {
                    getItem: () => null,
                    setItem: () => {},
                    removeItem: () => {}
                };
            }),
        }
    )
)
