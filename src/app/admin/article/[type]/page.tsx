"use client"
import React from "react";
import { z } from "zod";
import { pick } from "lodash";
import { $Enums } from "@prisma/client";
import Link from "next/link";
import { useParams, useSearchParams, useRouter } from "next/navigation";
import ArticleForm from "../components/ArticleForm";
import { ListSkeleton } from "~/components/common/Skeletons";
import { Button } from "~/components/ui/button";
import { useToast } from "~/hooks/use-toast";
import { useConfirmationStore } from "~/hooks/useConfirmationStore";
import { useLoading } from "~/hooks/useLoadingStore";
import useRightPanelStore from "~/hooks/useRightPanelStore";
import { api } from "~/trpc/react";
import { articleSchema } from "~/schemas/article";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { articleCategoryTypeMap } from "../../setting/article-category/components/CategoryForm";
import ArticleCard from "../components/ArticleCard";



export default function Page(): JSX.Element {
    const searchParams = useSearchParams();
    const router = useRouter();
    const { toast } = useToast();
    const { type } = useParams();
    const [showPanel, hidePanel] = useRightPanelStore((state) => [state.showPanel, state.hidePanel]);
    const [articleStatus, setArticleStatus] = React.useState<$Enums.ArticleStatus | "ALL">("ALL");
    const [articleType, setArticleType] = React.useState<$Enums.ArticleType | "ALL">("ALL");
    const articleCategoryType = React.useMemo(() => {
        if (type === "aec") {
            return $Enums.ArticleCategoryType.AEC;
        } else if (type === "aea") {
            return $Enums.ArticleCategoryType.AEA;
        }
        return $Enums.ArticleCategoryType.UNGROUPED;
    }, [type]);
    const { startLoading, stopLoading } = useLoading('global');
    const showConfirmation = useConfirmationStore((state) => state.showConfirmation);
    const queryCondition = {
        page: 1,
        pageSize: 10,
        categoryType: articleCategoryType,
    } as any;

    if (articleStatus !== "ALL") {
        queryCondition.status = articleStatus
    }
    if (articleType !== "ALL") {
        queryCondition.type = articleType
    }
    const { data, refetch, isLoading } = api.post.getAllArticles.useQuery(queryCondition);

    const createArticleMutation = api.post.createArticle.useMutation({
        onSuccess: () => {
            refetch();
            toast({
                title: "成功",
                description: "文章已成功添加",
            });
        },
        onError: () => {
            toast({
                title: "错误",
                description: "文章添加失败",
            });
        }
    });

    const updateArticleMutation = api.post.updateArticle.useMutation({
        onSuccess: () => {
            refetch();
            toast({
                title: "成功",
                description: "文章已成功更新",
            });
        },
        onError: () => {
            toast({
                title: "错误",
                description: "文章更新失败",
            });
        }
    });

    const deleteArticleMutation = api.post.deleteArticleById.useMutation({
        onSuccess: () => {
            refetch();
            toast({
                title: "成功",
                description: "已经成功删除该文章",
            });
        },
        onError: () => {
            toast({
                title: "错误",
                description: "删除该文章时发生错误",
                variant: "destructive",
            });
        },
    });


    React.useEffect(() => {
        refetch();
    }, [articleStatus]);

    const handleDelete = async (id: string) => {
        showConfirmation(
            "确定删除这个文章吗 ? 该文章下包含的所有内容都将被删除。",
            async () => {
                startLoading()
                await deleteArticleMutation.mutateAsync({ id: Number(id) });
                stopLoading()
            }
        );
    }

    const handleSubmitArticle = async (articleData: z.infer<typeof articleSchema>) => {
        if (articleData.id) {
            await updateArticleMutation.mutateAsync(articleData);
        } else {
            await createArticleMutation.mutateAsync(articleData);
        }
    }

    const updateQueryString = (params: { status?: string; type?: string }) => {
        const newSearchParams = new URLSearchParams(searchParams);

        Object.entries(params).forEach(([key, value]) => {
            if (value) {
                newSearchParams.set(key, value);
            } else {
                newSearchParams.delete(key);
            }
        });
        const newUrl = `${window.location.pathname}?${newSearchParams.toString()}`;
        router.push(newUrl);
    };


    React.useEffect(() => {
        const statusFromUrl = searchParams.get('status');
        if (statusFromUrl && Object.values($Enums.ArticleStatus).includes(statusFromUrl as $Enums.ArticleStatus)) {
            setArticleStatus(statusFromUrl as $Enums.ArticleStatus);
        }
    }, [searchParams]);

    return (
        <>
            <div className="flex justify-between items-center mb-4">
                <h1 className="text-2xl font-bold">
                    {articleCategoryTypeMap.get(articleCategoryType)}
                </h1>
                <div className="flex gap-2">
                    <Button onClick={() => {
                        showPanel(
                            "添加文章",
                            <ArticleForm
                                articleCategoryType={articleCategoryType}
                                onSubmit={handleSubmitArticle as any}
                            />
                        )
                    }} className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        添加文章
                    </Button>
                    <Link href={`/admin/setting/article-category`} passHref>
                        <Button asChild><span>添加分类</span></Button>
                    </Link>
                </div>
            </div>
            <div className="pb-4">
                <hr />
            </div>

            {isLoading && <ListSkeleton />}

            {!isLoading && (
                <>
                    <div className="flex gap-4 pb-4">
                        <Select
                            value={articleStatus}
                            onValueChange={(value) => {
                                setArticleStatus(value as $Enums.ArticleStatus | "ALL");
                                updateQueryString({ status: value });
                            }}
                        >
                            <SelectTrigger className="w-[100px] bg-white">
                                <SelectValue placeholder="选择状态" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="ALL">全部状态</SelectItem>
                                <SelectItem value={$Enums.ArticleStatus.PUBLISHED}>已发布</SelectItem>
                                <SelectItem value={$Enums.ArticleStatus.DRAFT}>草稿</SelectItem>
                                <SelectItem value={$Enums.ArticleStatus.ARCHIVED}>已归档</SelectItem>
                            </SelectContent>
                        </Select>

                        <Select
                            value={articleType}
                            onValueChange={(value) => {
                                setArticleType(value as $Enums.ArticleType | "ALL");
                                updateQueryString({ type: value });
                            }}
                        >
                            <SelectTrigger className="w-[100px] bg-white">
                                <SelectValue placeholder="选择类型" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="ALL">全部类型</SelectItem>
                                <SelectItem value={$Enums.ArticleType.NORMAL}>普通文章</SelectItem>
                                <SelectItem value={$Enums.ArticleType.VIDEO}>视频文章</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="pb-4">
                        <hr />
                    </div>

                    <div className="flex flex-col gap-4">
                        { data?.articles.length === 0 && <div className="text-center text-gray-500">暂无文章</div>}
                        {data?.articles.map((article) => (
                            <ArticleCard
                                key={article.id}
                                article={article}
                                onEdit={() => {
                                    showPanel(
                                        "编辑",
                                        <ArticleForm
                                            articleCategoryType={articleCategoryType}
                                            articleCoverImageUrl={article.articleCoverFile?.url}
                                            initialData={{
                                                ...pick(article, ['id', 'title', 'summary', 'content', 'tags', 'type', 'status', 'categoryId', 'articleCoverFileId', 'articleVideoFileId']),
                                                type: article.type as "NORMAL" | "VIDEO",
                                                categoryId: article.categoryId!,
                                                summary: article.summary || "",
                                                content: article.content || "",
                                            }}
                                            onSubmit={handleSubmitArticle as any}
                                        />
                                    );
                                }}
                                onDelete={() => handleDelete(article.id.toString())}
                            />
                        ))}
                    </div>
                </>
            )}
        </>
    )
}