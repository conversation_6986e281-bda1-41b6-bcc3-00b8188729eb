import { Admin<PERSON>eader } from "~/components/admin/layout/header";
import { type ReactNode } from "react";
import { AdminSideMenu } from "~/components/admin/layout/side-menu";

import { GlobalComponents } from "~/components/admin/layout/GlobalComponents";
import { redirect } from "next/navigation";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/server/auth";
import { $Enums } from "@prisma/client";
import { type Metadata } from "next";
import { ScrollArea } from "~/components/ui/scroll-area";
import SessionWrapper from "~/components/common/session-wrapper";

interface AdminLayoutProps {
  children: ReactNode;
}

export const metadata: Metadata = {
  title: "AEA线上教育平台和体验中心 - 管理后台",
  icons: [{ rel: "icon", url: "/alcon-logo.svg" }]
};
export default async function AdminLayout(props: AdminLayoutProps) {
  const session = await getServerSession(authOptions);

  if (
    !session?.user?.usergroups?.some((group) =>
      [
        $Enums.UserGroup.ADMIN as string,
        $Enums.UserGroup.SUPERADMIN as string,
        $Enums.UserGroup.SYSADMIN as string,
      ].includes(group),
    )
  ) {
    redirect("/admin-login");
  }

  return (
    <SessionWrapper session={session}>
    <div className="bg-white text-black">
      <div className="grid min-h-screen w-full md:grid-cols-[200px_1fr] lg:grid-cols-[250px_1fr]">
        <AdminSideMenu />
        <div className="flex h-screen flex-col">
          <AdminHeader />
          <div className="flex-1 w-full overflow-auto">
            <ScrollArea className=" p-8">
              {props.children}
            </ScrollArea>
          </div>
        </div>
        <GlobalComponents />
      </div>
    </div>
    </SessionWrapper>
  );
}
