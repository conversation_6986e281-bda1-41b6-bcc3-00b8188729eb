"use client";

import React from "react";
import AsideNav from "~/components/admin/layout/aside";

import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "~/components/ui/tabs";
import { appointmentNavs } from "~/constants/navs";
import { api } from "~/trpc/react";
import type { RouterOutputs } from "~/trpc/react";
import { $Enums } from "@prisma/client";
import type { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { DataTable } from "~/components/common/DataTable";
import { produce } from "immer";
import useRightPanelStore from "~/hooks/useRightPanelStore";
import TableOperation from "~/components/common/TableOperation";
import {  FileCheck2,  UsersRound } from "lucide-react";
import { className1 } from "../project/utils/class-util";
import Detail from "./components/Detail";
import Customer from "./components/Customer";
import { labels } from "./components/labels";
import { usePageNavigation } from "~/hooks/use-pagenavigation";
import { ListSkeleton } from "~/components/common/Skeletons";
import useLocaltionSelector from "~/hooks/useLocationSelector";
import UserLink from "./components/UserLink";
import ExportDialog from "./components/ExportDialog";
import ExportCustomersDialog from "./components/ExportCustomersDialog";
import ProjectHeader from "./components/ProjectHeader";
import SortableHeader from "./components/SortableHeader";

 

const columns: ColumnDef<
  RouterOutputs["appointment"]["getAllAppointments"]["data"][number]
>[] = [
  {
    accessorKey: "id",
    header: "ID",
    size: 60,
  },

  {
    accessorKey: "projectName",
    header:  ProjectHeader,
    size: 150,
    cell: ({ row }) => (
      <span className="rounded-sm bg-sky-500 px-2 py-1 text-sm text-white">
        {row.original.appointmentProject.name}
      </span>
    ),
  },
  {
    accessorKey: "remark",
    header: "创建人",
    size: 150,
    cell: ({ row }) => {
      const createdBy = row.original.AppointmentOperationLog[0]?.createdBy;
      return (
        <UserLink id={createdBy?.id ?? ""} name={createdBy?.name ?? null} />
      );
    },
  },
  {
    accessorKey: "customers",
    header: "预约人数",
    size: 100,
    cell: ({ row }) => <span>{row.original.customersCount}</span>,
  },
  {
    accessorKey: "timeSlot",
    header: () => {
      return (
        <SortableHeader 
          field="startTime" 
          label="预约时间段" 
        />
      )
    },
    size: 300,
    cell: ({ row }) => (
      <div className="flex flex-row gap-1 font-mono text-xs">
        <span>
          {format(
            new Date(row.original.timeSlot.startTime),
            "yyyy-MM-dd HH:mm",
          )}
        </span>
        <span>~</span>
        <span>
          {format(new Date(row.original.timeSlot.endTime), "yyyy-MM-dd HH:mm")}
        </span>
      </div>
    ),
  },
  {
    accessorKey: "createdAt",
    header: () => {
      return (
        <SortableHeader 
          field="createdAt" 
          label="创建时间" 
        />
      )
    },
    size: 150,
    cell: ({ row }) => (
      <span className="font-mono text-xs">
        {format(new Date(row.original.createdAt), "yyyy-MM-dd HH:mm")}
      </span>
    ),
  },
];

export default function Page(): JSX.Element {
  const {
    pageNumber,
    pageSizeNumber,
    getSearchParam,
    updatePageParam,
    createPagination,
  } = usePageNavigation();
  const locationId = getSearchParam("locationId");
  const { activeLocationId, activeLocationName, createLocaltionOptions } =
    useLocaltionSelector(locationId, (id) => {
      updatePageParam(
        {
          locationId: String(id),
        },
        false,
      );
    });

  const [showPanel] = useRightPanelStore((state) => [state.showPanel]);
  const status = getSearchParam("status");
  const projectId = getSearchParam("projectId");
  const sortBy = getSearchParam("sortBy");
  const sortOrder = getSearchParam("sortOrder");
  
  const paramStatus =
    Object.values($Enums.AppointmentStatus).find((s) => s === status) ??
    $Enums.AppointmentStatus.CREATED;

  const { data, isLoading, isRefetching } =
    api.appointment.getAllAppointments.useQuery(
      {
        locationId: Number(activeLocationId),
        projectId: projectId ? Number(projectId) : undefined,
        page: pageNumber,
        pageSize: pageSizeNumber,
        status: paramStatus,
        sortBy: sortBy ?? undefined,
        sortOrder: sortOrder ?? undefined,
      },
      {
        enabled: !!activeLocationId,
      },
    );

  const tableColumns = React.useMemo<typeof columns>(() => {
    return produce(columns, (draft: ColumnDef<any>[]) => {
      draft.push({
        accessorKey: "actions-column",
        header: "操作",
        size: 40,
        meta: { align: "text-right" },
        cell: ({ row }) => {
          const baseTitle =
            paramStatus !== $Enums.AppointmentStatus.CREATED
              ? "查看预约"
              : "预约审核";
          const operations = [
            {
              label: baseTitle,
              onClick: () => {
                showPanel(baseTitle, <Detail id={Number(row.original.id)} />);
              },
              icon: FileCheck2,
              className: className1,
            },
          ];

          if (
            paramStatus !== $Enums.AppointmentStatus.CHECKED &&
            row.original.appointmentProject.type !==
              $Enums.AppointmentProjectType.LIVE
          ) {
            const title = `${row.original.appointmentProject.name} ${format(
              new Date(row.original.timeSlot.startTime as Date),
              "yyyy-MM-dd HH:mm",
            )} - ${format(new Date(row.original.timeSlot.endTime as Date), "yyyy-MM-dd HH:mm")}`;
            operations.push({
              label: "预约人名单",
              onClick: () => {
                showPanel(
                  `${title} 预约人名单`,
                  <Customer
                    id={Number(row.original.id)}
                    title={title}
                    type={row.original.appointmentProject.type}
                  />,
                );
              },
              icon: UsersRound,
              className: className1,
            });
          }

          return (
            <div className="w-full text-right">
              <TableOperation operations={operations} label="操作" />
            </div>
          );
        },
      });
    });
  }, [showPanel, paramStatus]);

  return (
    <>
      <AsideNav activeId={1} title="预约" navs={appointmentNavs}>
        <div className="flex px-1">{createLocaltionOptions()}</div>
      </AsideNav>

      <div className="bg-neutral-400/20 p-8">
        <div className="flex flex-row justify-between">
          <Tabs value={paramStatus} className="w-[400px]">
            <TabsList className="bg-slate-100">
              {Object.entries(labels).map(([key, value]) => (
                <TabsTrigger
                  key={key}
                  value={key}
                  onClick={() => {
                    updatePageParam(
                      {
                        status: key,
                        locationId: String(activeLocationId),
                      },
                      false,
                    );
                  }}
                >
                  {value}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
          <div className="flex flex-row gap-2">
            {data &&
            data?.totalCount > 0 &&
            (paramStatus === $Enums.AppointmentStatus.CONFIRMED ||
              paramStatus === $Enums.AppointmentStatus.CHECKED) ? (
              <ExportDialog
                status={paramStatus}
                locationId={String(activeLocationId)}
                locationName={activeLocationName ?? ""}
                projectId={projectId ?? ""}
                sortBy={sortBy ?? ""}
                sortOrder={sortOrder ?? ""}
              />
            ) : null}
            {
              paramStatus === $Enums.AppointmentStatus.CHECKED  && (
                <ExportCustomersDialog
                  status={paramStatus}
                  locationId={String(activeLocationId)}
                  locationName={activeLocationName ?? ""}
                />
              )
            }
          </div>
        </div>

        <div className="mt-4 bg-white p-8">
          {!activeLocationId || isLoading || isRefetching ? (
            <ListSkeleton />
          ) : (
            <DataTable
              isRefetching={isRefetching}
              columns={tableColumns}
              data={data?.data ?? []}
            />
          )}
        </div>
        {data?.page && data?.totalPages ? (
          <div className="pt-8">
            {createPagination(data.page, data.totalPages)}
          </div>
        ) : null}
      </div>
    </>
  );
}
