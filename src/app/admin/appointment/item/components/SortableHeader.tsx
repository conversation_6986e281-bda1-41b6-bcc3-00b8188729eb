"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowUpWideN<PERSON>row, ClockArrowDown, ClockArrowUp } from "lucide-react";
import { usePageNavigation } from "~/hooks/use-pagenavigation";
import { cn } from "~/lib/utils";

interface SortableHeaderProps {
  field: string;
  label: string;
}

const sortOrderCycle = ["asc", "desc", ""];

export default function SortableHeader({ field, label }: SortableHeaderProps) {
  const { getSearchParam, updatePageParam } = usePageNavigation();
  const sortBy = getSearchParam("sortBy");
  const sortOrder = getSearchParam("sortOrder");

  const isActive = sortBy === field;

  const getNextSortOrder = (): string => {
    if (!isActive) return "asc";

    const currentIndex = sortOrderCycle.indexOf(sortOrder ?? "");
    const nextIndex = (currentIndex + 1) % sortOrderCycle.length;
    return sortOrderCycle[nextIndex] ?? "";
  };

  const handleClick = () => {
    const nextSortOrder = getNextSortOrder();

    if (nextSortOrder) {
      updatePageParam({
        sortBy: field,
        sortOrder: nextSortOrder,
      });
    } else {
      updatePageParam({
        sortBy: "",
        sortOrder: "",
      });
    }
  };

  return (
    <div className="flex flex-row gap-1 items-center">
      <span>{label}</span>
      <span
        className={cn("cursor-pointer", isActive ? "text-blue-500" : "text-gray-500")}
        onClick={handleClick}
      >
        {sortOrder === "asc" ? (
          <ClockArrowDown  className="w-4 h-4" />
        ) : (
          <ClockArrowUp className="w-4 h-4" />
        )}
      </span>
    </div>
  );
}
