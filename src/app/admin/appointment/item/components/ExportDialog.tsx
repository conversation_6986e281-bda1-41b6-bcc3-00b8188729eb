"use client";
import { Dialog, DialogTrigger,DialogHeader , DialogContent, DialogTitle, DialogDescription } from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";


import type { $Enums } from "@prisma/client";

import React from "react";
import ExportAppointments from "./ExportAppointments";

interface ExportDialogProps {
    locationName: string;
    status: $Enums.AppointmentStatus;
    locationId: string;
    projectId: string;
    sortBy: string | undefined;
    sortOrder: string | undefined;
}

export default function ExportDialog({   status, locationId, locationName, projectId, sortBy, sortOrder }: ExportDialogProps) {
    const [open, setOpen] = React.useState(false);
    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button variant="outline">导出预约数据</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>导出预约数据</DialogTitle>
                    <DialogDescription>
                        正在导出数据, 请稍后...
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <ExportAppointments
                        {...{
                            status,
                            locationId,
                            locationName: locationName,
                            projectId: projectId ? Number(projectId) : undefined,
                            sortBy: sortBy ?? "",
                            sortOrder: sortOrder ?? "",
                            onExportFinished: () => setOpen(false)
                        }}
                    />
                </div>

            </DialogContent>
        </Dialog>
    )
}