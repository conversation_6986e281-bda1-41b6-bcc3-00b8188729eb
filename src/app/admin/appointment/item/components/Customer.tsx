import { Contact, FileDown, FileUp, Signature, Trash2 } from "lucide-react";
import { Button } from "~/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import useExcel from "~/hooks/useExcel";
import { template, internalTemplate, genrateContactCsv, generateSignInSheet } from "./template";
import { api } from "~/trpc/react";
import { $Enums } from "@prisma/client";
import { ListSkeleton } from "~/components/common/Skeletons";
import React from "react";

import type { UnifiedDataStruct, UnifiedInternalCustomersDataStruct } from "./type";
import { Separator } from "~/components/ui/separator";
import { useToast } from "~/hooks/use-toast";
import useModalStore from "~/hooks/useModalStore";
import { ImportModal } from "./ImportModal";
import useRightPanelStore from "~/hooks/useRightPanelStore";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { format } from "date-fns";
import { useConfirmationStore } from "~/hooks/useConfirmationStore";
import { useLoading } from "~/hooks/useLoadingStore";
import { util } from "zod";

interface CustomerProps {
  id: number;
  title: string;
  type: $Enums.AppointmentProjectType;
}


export enum AppointmentCustomerType {
  INTERNAL = "internal",
  EXTERNAL = "external",
  UNKNOWN = "unknown",
}

export default function Customer({ id, title, type }: CustomerProps) {
  const utils = api.useUtils();
  const { toast } = useToast();
  const { showModal } = useModalStore();
  const showConfirmation = useConfirmationStore(
    (state) => state.showConfirmation,
  );
  const { startLoading, stopLoading } = useLoading("global");
  const parsedDataRef = React.useRef<any[]>([]);
  const appointmentTypeRef = React.useRef<AppointmentCustomerType>();
  const [selectedCustomerType, setSelectedCustomerType] = React.useState<AppointmentCustomerType>();
  const [hidePanel] = useRightPanelStore((state) => [state.hidePanel]);
  const { data, isLoading, isRefetching } =
    api.appointment.getAppointmentCustomers.useQuery(
      {
        id: id,
      },
      {
        enabled: !!id,
      },
    );

  const deleteCustomerMutation = api.appointment.deleteCustomer.useMutation();
  const deleteInternalCustomerMutation = api.appointment.deleteInternalCustomer.useMutation();

  const customerType = React.useMemo<AppointmentCustomerType>(() => {
    if (data?.data?.internalCustomers && data?.data?.internalCustomers.length > 0) {
      return AppointmentCustomerType.INTERNAL;
    } else if (data?.data?.customers && data?.data?.customers.length > 0) {
      return AppointmentCustomerType.EXTERNAL;
    } else {
      return AppointmentCustomerType.UNKNOWN;
    }
  }, [data]);

  React.useEffect(() => {
    appointmentTypeRef.current = selectedCustomerType;
  }, [selectedCustomerType]);


  React.useEffect(() => {
    if (customerType === AppointmentCustomerType.INTERNAL) {
      setSelectedCustomerType(AppointmentCustomerType.INTERNAL);
    } else if (customerType === AppointmentCustomerType.EXTERNAL) {
      setSelectedCustomerType(AppointmentCustomerType.EXTERNAL);
    }
  }, [customerType]);

  const { parseCSV, generateCSVTemplate } = useExcel();
  const formattedData = React.useMemo<(UnifiedDataStruct | UnifiedInternalCustomersDataStruct)[]>(() => {

    if (data?.data) {
      const projectName = data?.data.appointmentProject.name;
      const timeSlot = `${format(data?.data.timeSlot.startTime, "yyyy-MM-dd HH:mm")} ~ ${format(data?.data.timeSlot.endTime, "yyyy-MM-dd HH:mm")}`;
      const createdBy = data?.data.AppointmentOperationLog[0]?.createdBy?.name ?? "";
      if (customerType === AppointmentCustomerType.INTERNAL) {
        return data?.data.internalCustomers.map(
          (item, index) =>
            ({
              id: index,
              customerId: item.id,
              name: item.name,
              department: item.department,
              projectName: projectName,
              timeSlot: timeSlot,
              createdBy: createdBy,
            }) as UnifiedInternalCustomersDataStruct,
        );
      } else {

        return data?.data.customers.map(
          (item, index) =>
            ({
              id: index,
              customerId: item.id,
              name: item.name,
              hospitalName: item.hospital.name,
              projectName: projectName,
              timeSlot: timeSlot,
              createdBy: createdBy,
              contactInfo: item.contactInfo,
            }) as UnifiedDataStruct,
        );
      }
    }
    return [];
  }, [data?.data, customerType]);

  const handleDownload = React.useCallback(() => {
    if (selectedCustomerType === AppointmentCustomerType.INTERNAL) {
      generateCSVTemplate(internalTemplate, title + "内部名单导入模板");
    } else {
      generateCSVTemplate(template, title + "外部名单导入模板");
    }
  }, [title, selectedCustomerType, generateCSVTemplate]);

  const handleDelete = async (type: AppointmentCustomerType, id: number, info: string) => {
    console.log("handleDelete", id, info);
    showConfirmation(
      `点击确定将从名单列表内删除: ${info} `,
      async () => {
        startLoading();
        let res;
        if (type === AppointmentCustomerType.EXTERNAL) {
          res = await deleteCustomerMutation.mutateAsync({ id });
        } else if (type === AppointmentCustomerType.INTERNAL) {
          res = await deleteInternalCustomerMutation.mutateAsync({ id });
        }

        if (res?.status === "success") {
          toast({
            title: "删除成功"
          });
        } else {
          toast({
            title: "删除失败",
            description: "请稍后再试",
            variant: "destructive",
          });
        }
        stopLoading();
        utils.appointment.getAppointmentCustomers.invalidate();
        utils.appointment.getAllAppointments.invalidate();
        hidePanel();
      },
    );
  }

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;
    try {
      const parsedData = await parseCSV(file);
      parsedDataRef.current = parsedData?.Sheet1?.slice(1) ?? [];
      hidePanel();
      showModal(
        "预约人名单导入",
        <ImportModal
          data={parsedDataRef.current}
          id={id}
          type={appointmentTypeRef.current as AppointmentCustomerType}
        />,
      );
    } catch (error) {
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "解析CSV文件失败",
        variant: "destructive",
      });
    }

    // Reset the input
    event.target.value = "";
  };

  const downloadContactCsv = React.useCallback(() => {
    const contactCsv = genrateContactCsv(title, formattedData);
    generateCSVTemplate(contactCsv, title + "预约人名单");
  }, [title, formattedData, generateCSVTemplate]);
  const printSignInSheet = React.useCallback(() => {
    const template = generateSignInSheet(title, formattedData);

    const printWindow = window.open("", "_blank");
    printWindow?.document.write(template);
    printWindow?.document.close();
    printWindow?.focus();
    printWindow?.print();
  }, [title, formattedData]);

  if (isLoading || isRefetching) {
    return <ListSkeleton />;
  }
  return (
    <div className="flex flex-col">
      {
        data?.data?.appointmentProject.type === $Enums.AppointmentProjectType.TRAINING && (
          <div className="p-1 py-4">
            <Select
              value={selectedCustomerType}
              onValueChange={(value) => setSelectedCustomerType(value as AppointmentCustomerType)}
              disabled={customerType !== AppointmentCustomerType.UNKNOWN}
            >
              <SelectTrigger className="w-[180px] bg-white">
                <SelectValue placeholder="请选择培训客户类型" />
              </SelectTrigger>
              <SelectContent >
                <SelectGroup>
                  <SelectItem value={AppointmentCustomerType.INTERNAL}>内部客户</SelectItem>
                  <SelectItem value={AppointmentCustomerType.EXTERNAL}>外部客户</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
        )}
      {
        formattedData.length > 0 && (
          <div className="bg-white p-8 shadow-lg">


            <Table className="">
              {/* <TableCaption></TableCaption> */}
              <TableHeader>
                {
                  customerType === AppointmentCustomerType.EXTERNAL && (
                    <TableRow>
                      <TableHead className="w-[100px]">编号</TableHead>
                      <TableHead>姓名</TableHead>
                      <TableHead>所在医院</TableHead>
                      <TableHead className="text-right">联系方式</TableHead>
                      {
                        type === $Enums.AppointmentProjectType.TRAINING && (
                          <TableHead className="text-right">操作</TableHead>
                        )
                      }

                    </TableRow>
                  )
                }
                {
                  customerType === AppointmentCustomerType.INTERNAL && (
                    <TableRow>
                      <TableHead className="w-[100px]">编号</TableHead>
                      <TableHead>姓名</TableHead>
                      <TableHead>部门</TableHead>
                      {
                        type === $Enums.AppointmentProjectType.TRAINING && (
                          <TableHead className="text-right">操作</TableHead>
                        )
                      }
                    </TableRow>
                  )
                }
              </TableHeader>
              <TableBody>
                {customerType === AppointmentCustomerType.EXTERNAL && (formattedData as UnifiedDataStruct[]).map((customer, index) => (
                  <TableRow key={customer.id}>
                    <TableCell className="font-medium">{index + 1}</TableCell>
                    <TableCell>{customer.name}</TableCell>
                    <TableCell>{customer.hospitalName}</TableCell>
                    <TableCell className="text-right">
                      {customer.contactInfo}
                    </TableCell>
                    {
                      type === $Enums.AppointmentProjectType.TRAINING && (
                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDelete(AppointmentCustomerType.EXTERNAL, customer.customerId, `${customer.name} ${customer.hospitalName} ${customer.contactInfo}`)}>
                            <Trash2 />
                          </Button>
                        </TableCell>
                      )
                    }
                  </TableRow>
                ))}
                {customerType === AppointmentCustomerType.INTERNAL && (formattedData as UnifiedInternalCustomersDataStruct[]).map((customer, index) => (
                  <TableRow key={customer.id}>
                    <TableCell className="font-medium">{index + 1}</TableCell>
                    <TableCell>{customer.name}</TableCell>
                    <TableCell>{customer.department}</TableCell>
                    {
                      type === $Enums.AppointmentProjectType.TRAINING && (
                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDelete(AppointmentCustomerType.INTERNAL, customer.customerId, `${customer.name} ${customer.department}`)}>
                            <Trash2 />
                          </Button>
                        </TableCell>
                      )
                    }
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <Separator />
            <div className="flex justify-center gap-4 p-4">
              <Button
                variant="outline"
                onClick={downloadContactCsv}
              >
                <Contact />
                下载名单
              </Button>
              <Button
                variant="outline"
                onClick={printSignInSheet}
              >
                <Signature />
                打印签到表
              </Button>
            </div>
          </div>
        )
      }


      {type === $Enums.AppointmentProjectType.TRAINING && (
        <div className=" bg-white p-8 shadow-lg">
          <h2>名单导入</h2>
          <Separator />

          <div className="mx-auto w-full bg-white py-12 text-center">
            <p className="leading-10">导入说明</p>
            <p className="text-sm font-light leading-6 text-gray-500">
              1. 系统不校验重复姓名或联系方式。
            </p>
            <p className="text-sm font-light leading-6 text-gray-500">
              2.可分次导入，但请避免重复。
            </p>
            <p className="text-sm font-light leading-6 text-gray-500">
              3.外部客户需填医院名称或SFID，错误内容将被丢弃。
            </p>
            <p className="text-sm font-light leading-6 text-gray-500">
              4. 首次导入请选择客户类型，完成导入后客户类型不可更改。
            </p>
            <p className="text-sm font-light leading-6 text-gray-500">
              5. 请按模板格式填写并选择正确客户类型，否则无法导入。
            </p>
            <p className="text-sm font-light leading-6 text-gray-500">
              6.  已经导入的数据可以删除，但请注意，删除后不可恢复。
            </p>
          </div>
          <Separator />

          <div className="flex flex-row justify-center gap-2 py-8">
            <Button
              onClick={handleDownload}
              type="button"
              className="inline-flex items-center rounded-md bg-blue-900/90 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-900"
              disabled={!selectedCustomerType || selectedCustomerType === AppointmentCustomerType.UNKNOWN}
            >
              <FileDown aria-hidden="true" className="-ml-0.5 mr-1.5 h-5 w-5" />
              下载预约人名单模板
            </Button>

            <Button
              type="button"
              className="inline-flex items-center rounded-md bg-indigo-900/90 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-900"
              onClick={() => {
                document.getElementById("csvFileInput")?.click();
              }}
              disabled={!selectedCustomerType || selectedCustomerType === AppointmentCustomerType.UNKNOWN}
            >
              <FileUp aria-hidden="true" className="-ml-0.5 mr-1.5 h-5 w-5" />
              上传并导入预约人名单
            </Button>
          </div>

          <input
            id="csvFileInput"
            type="file"
            accept=".csv"
            className="hidden"
            onChange={handleFileUpload}
          />
        </div>
      )}
    </div>
  );
}
