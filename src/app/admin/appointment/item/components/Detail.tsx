import React from "react";
import { format } from "date-fns";
import Image from "next/image";
import { ListSkeleton } from "~/components/common/Skeletons";
import { Button } from "~/components/ui/button";
import { api } from "~/trpc/react";
import { labels } from "./labels";
import { $Enums } from "@prisma/client";
import { useToast } from "~/hooks/use-toast";
import { Separator } from "~/components/ui/separator";
import UserLink from "./UserLink";

interface DetailProps {
  id: number;
}
export default function Detail({ id }: DetailProps) {
  const utils = api.useUtils();
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = React.useState(false);
  const { mutateAsync } = api.appointment.addAppointmentOperationLog.useMutation();

  const { data, isLoading, refetch, isRefetching, error } =
    api.appointment.getAppointmentById.useQuery(
      {
        id: id,
      },
      {
        enabled: !!id,
      },
    );

  const handleSubmit = React.useCallback(async (status: $Enums.AppointmentStatus) => {
    setIsProcessing(true);
    const result = await mutateAsync({
      id,
      status
    })
    setIsProcessing(false);
    if (result.status === "success") {
      toast({
        variant: "default",
        title: "操作成功",
        description: "状态更新成功",
      });
      await utils.invalidate(undefined, {
        queryKey: ["appointment.getAppointmentById", { id: id }],
        refetchType: 'active',
      });

    } else {
      toast({
        variant: "destructive",
        title: "操作失败",
        description: "状态更新失败, 请稍后再试",
      });
    }
  }, [id])

  if (isLoading) {
    return <ListSkeleton />;
  }
  return (
    <div className="pr-2">
      <div className="w-full flex flex-col gap-6">
        <div className="bg-white shadow-lg  p-8">
          <h5 className="font-bold">报名信息</h5>
          <Separator />
          <div className="w-full font flex flex-col  text-sm mt-6 divide-y divide-slate-900/10 bg-gray-100 p-4">
            <div className="w-full py-2 flex flex-row  justify-between">
              <span>报名项目:</span>

              <span className="bg-sky-500 px-2 py-1 text-white text-sm rounded-sm">
                {data?.appointmentProject.name}
              </span>
            </div>
            <div className="w-full py-2 flex flex-row  items-center justify-between">
              <span>预约时间段:</span>
              <span className="flex flex-row gap-1 items-center font-sans">
                {data?.timeSlot.startTime && (
                  <span className="  bg-violet-900  px-2 py-1 text-white text-sm rounded-sm">
                    {format(
                      new Date(data?.timeSlot.startTime),
                      "yyyy-MM-dd HH:mm",
                    )}
                  </span>
                )}
                <span>-</span>
                {data?.timeSlot.endTime && (
                  <span className="bg-violet-900  px-2 py-1 text-white text-sm rounded-sm">
                    {format(
                      new Date(data?.timeSlot.endTime),
                      "yyyy-MM-dd HH:mm",
                    )}
                  </span>
                )}
              </span>
            </div>
            <div className="w-full py-2 flex flex-row  justify-between">
              <span>预约人数:</span>
              <span className=" text-gray-500">
                <span className=" bg-blue-900  px-2 py-1 text-white text-sm rounded-sm">
                  {data?.customersCount}
                </span>
              </span>
            </div>
            <div className="w-full py-2 flex flex-col gap-2 items-start ">
              <span>备注:</span>
              <span className="w-full p-4 text-sm  bg-slate-200 text-gray-500">
                {data?.remark != "" ? data?.remark : "未添加备注"}
              </span>
            </div>

            {data?.status === $Enums.AppointmentStatus.CREATED && (
              <div className="pt-4">
                <span className="flex flex-row gap-2">
                  <Button
                    disabled={isProcessing}
                    variant="default"
                    className=" !bg-blue-700 !hover:bg-blue-600"
                    onClick={() => handleSubmit($Enums.AppointmentStatus.CONFIRMED)}
                  > 审核通过</Button>
                  <Button
                    disabled={isProcessing}
                    variant="destructive"
                    onClick={() => handleSubmit($Enums.AppointmentStatus.REJECTED)}
                  >审核不通过</Button>
                </span>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white shadow-lg p-8">
          <h5 className="font-bold">时间线</h5>
          <Separator />
          <ul className="w-full flex flex-col items-start gap-1 px-2 pt-6">
            {data?.AppointmentOperationLog.map((item, index) => {
              const prevStatus = index > 0 ? data?.AppointmentOperationLog[index - 1]?.status : null;
              return (
                <li key={item.id} className="relative flex w-full items-start justify-start gap-2">
                  <span className="h-full w-3">
                    <span className="absolute left-0.5 top-0 h-full w-2 rounded-full bg-gray-400/20"></span>
                    <span className="absolute left-0 top-0 h-3 w-3 rounded-full bg-gray-400"></span>
                  </span>

                  <span className="flex h-full flex-col gap-0">
                    <p className="text-sm font-light leading-none">
                      {format(new Date(item.createdAt), "yyyy-MM-dd HH:mm")}
                    </p>
                    {
                      item.createdBySystem ? (
                        <p className="font-light leading-8">
                          <span className=" bg-blue-900  px-2 py-1 text-white text-xs font-light rounded-sm">
                            系统已自动将该预约状态设置为：{labels[item.status]}
                          </span>
                        </p>
                      ) : (
                        <>
                          {
                            prevStatus !== item.status && (
                              <p className="font-light leading-8">
                                <span className=" bg-zinc-500  px-2 py-1 text-white text-xs font-light rounded-sm">
                                  {labels[item.status]}
                                </span>
                              </p>
                            )
                          }

                          {
                            item.status === $Enums.AppointmentStatus.CHECKED &&
                            (item.verification?.appointmentVerificationFile?.url ? (
                              <Image
                                width={"600"} height={"600"}
                                src={item.verification?.appointmentVerificationFile?.url}
                                alt="签到图片"
                              />
                            ) : "未正确上传签到表图片")
                          }
                          {
                            item.message && <p className="w-full text-sm font-light ">{item.message}</p>
                          }
                          <UserLink

                            id={item.createdBy?.id ?? ""}
                            name={item.createdBy?.name ?? null}
                            className="text-xs font-thin leading-6 bg-transparent text-blue-500 p-0"
                          />
                          {/* <p className="flex items-center text-sm font-thin leading-6">
                            {item.createdBy?.name ?? "已失效用户"}
                          </p> */}

                        </>
                      )
                    }
                  </span>
                </li>
              );
            })}
          </ul>
        </div>
      </div>
    </div>
  );
}
