/* eslint-disable @typescript-eslint/restrict-template-expressions */
import { chunk} from "lodash";
import type {
  UnifiedDataStruct,
  UnifiedInternalCustomersDataStruct,
} from "./type";
import { RouterOutputs } from "~/trpc/react";
import { format } from "date-fns";

export const generateAppointmentCsv = (
  title: string,
  data: RouterOutputs["appointment"]["getAllAppointments"]["data"],
) => ({
  [title]: {
    data: [
      [
        "预约编号",
        "预约项目",
        "创建人",
        "预约人数",
        "预约时间段",
        "创建时间",
        "备注",
      ],
      ...data.map((item) => [
        item.id,
        item.appointmentProject.name,
        item.AppointmentOperationLog?.[0]?.createdBy?.name ?? "",
        item.customersCount,
        `${format(new Date(item.timeSlot.startTime), "yyyy-MM-dd HH:mm")} ~ ${format(new Date(item.timeSlot.endTime), "yyyy-MM-dd HH:mm")}`,
        format(item.createdAt, "yyyy-MM-dd HH:mm"),
        item.remark,
      ]),
    ],
  },
});


export const generateCustomersCsv = (
  title: string,
  data: RouterOutputs["appointment"]["getAllCustomers"],
) => ({
  [title]: {
    data: [
		["#", "姓名", "医院", "SFID", "联系方式", "项目", "预约时间段", "创建人"],
		...data.map((item, index) => [
			`${index + 1}`,
			item.name,
			item.hospital.name,
			item.hospital.sfid,
			item.contactInfo,
			item.appointments.appointmentProject.name,
			`${format(new Date(item.appointments.timeSlot.startTime), "yyyy-MM-dd HH:mm")} ~ ${format(new Date(item.appointments.timeSlot.endTime), "yyyy-MM-dd HH:mm")}`,
			item.appointments.AppointmentOperationLog?.[0]?.createdBy?.name ?? "",
		]),
	]
  }
})




export const template = {
  Sheet1: {
    data: [
      ["姓名", "医院名称/医院SFID （任填其一）", "联系方式 （手机号）"],
      ["", "", ""],
    ],
  },
};

export const internalTemplate = {
  Sheet1: {
    data: [
      ["姓名", "部门"],
      ["", ""],
    ],
  },
};

export const genrateContactCsv = (
  title: string,
  data: (UnifiedDataStruct | UnifiedInternalCustomersDataStruct)[],
) => {
  if (data.length === 0) {
    return { [title]: { data: [["暂无数据"]] } };
  }

  // Determine the type based on the first item
  const isUnifiedDataStruct = "hospitalName" in data[0]!;

  return {
    [title]: {
      data: [
        isUnifiedDataStruct
          ? ["#", "姓名", "医院", "项目", "预约时间段", "创建人",  "联系方式"] // UnifiedDataStruct headers
          : ["#", "姓名", "部门", "预约项目", "预约时间段", "创建人"], // UnifiedInternalCustomersDataStruct headers
        ...data.map((item, index) => {
          if (isUnifiedDataStruct) {
            const unifiedItem = item as UnifiedDataStruct;
            return [
              `${index + 1}`,
              unifiedItem.name,
              unifiedItem.hospitalName,
              unifiedItem.projectName,
              unifiedItem.timeSlot,
              unifiedItem.createdBy,
              unifiedItem.contactInfo,
            ];
          } else {
            const internalItem = item as UnifiedInternalCustomersDataStruct;
            return [
				`${index + 1}`, 
				internalItem.name, 
				internalItem.department,
				internalItem.projectName,
				internalItem.timeSlot,
				internalItem.createdBy,
			];
          }
        }),
      ],
    },
  };
};

export const generateSignInSheet = (
  title: string,
  data: (UnifiedDataStruct | UnifiedInternalCustomersDataStruct)[],
) => {
  if (data.length === 0) {
    return "<h2>暂无数据</h2>";
  }

  // Determine the type of the first entry
  const isUnifiedDataStruct = "hospitalName" in data[0]!;

  // Set appropriate headers based on type
  const headers = isUnifiedDataStruct
    ? ["#", "预约信息", "签名栏"] // UnifiedDataStruct
    : ["#", "预约信息", "签名栏"]; // UnifiedInternalCustomersDataStruct

  const headerPercent = ["10%", "60%", "30%"];

  const chunkData = chunk(data, 5);

  return `
	<!DOCTYPE html>
	<html lang="zh">
	<head>
	  <meta charset="UTF-8">
	  <meta name="viewport" content="width=device-width, initial-scale=1.0">
	  <title>${title} 签到表</title>
	  <style>
		body {
		  margin: 0;
		  padding: 0;
		  font-family: Arial, sans-serif;
		  background: white;
		}
  
		.page {
		  width: 210mm;
		  min-height: 297mm;
		  padding: 10mm;
		  margin: 0 auto;
		  box-sizing: border-box;
		  page-break-after: always;
		}
  
		.header {
		  text-align: center;
		  font-size: 20px;
		  font-weight: bold;
		}
  
		table {
		  width: 100%;
		  border-collapse: collapse;
		  margin-top: 10mm;
		}
  
		table, th, td {
		  border: 1px solid #ddd;
		}
  
		th {
		  background-color: #f2f2f2;
		  font-weight: bold;
		  padding: 10px;
		  text-align: left;
		}
  
		td {
		  padding: 8px;
		  text-align: left;
		}
		td span {
			display: block;
			line-height: 1.5;
		}
  
		tr:hover {
		  background-color: #f9f9f9;
		}
  
		@media print {
		  @page {
			size: A4;
			margin: 10mm;
		  }
  
		  .page {
			page-break-after: always;
		  }
  
		  table {
			page-break-inside: avoid;
		  }
  
		  tbody > tr {
			height: 20mm;
		  }
		}
	  </style>
	</head>
	<body>
	  ${chunkData
      .map(
        (chunk, pageIndex) => `
		  <div class="page">
			<div class="header">
			  <h2>${title} 签到表</h2>
			</div>
			<table>
			  <thead>
				<tr>
				  ${headers.map((header, index) => `<th align="left" width="${headerPercent[index]}">${header}</th>`).join("")}
				</tr>
			  </thead>
			  <tbody>
				${chunk
          .map((item, index) => {
            const rowIndex = pageIndex * 10 + index + 1;
			if (isUnifiedDataStruct) {
				const unifiedItem = item as UnifiedDataStruct;
				return `
						<tr>
							<td>${rowIndex}</td>
							<td>
								<span style="padding: 2mm;">
									<span>姓名：${unifiedItem.name}</span>
									<span>医院：${unifiedItem.hospitalName}</span>
									<span>项目：${unifiedItem.projectName}</span>
									<span>时间：${unifiedItem.timeSlot}</span>
									<span>创建人：${unifiedItem.createdBy}</span>
								</span>
							</td>
							<td></td>
						</tr>`
			} else {
				const internalItem = item as UnifiedInternalCustomersDataStruct;
				return `
						<tr>
						  <td>${rowIndex}</td>
						  <td>
								<span style="padding: 2mm;">
									<span>姓名：${internalItem.name}</span>
									<span>部门：${internalItem.department}</span>
									<span>预约项目：${internalItem.projectName}</span>
									<span>预约时间：${internalItem.timeSlot}</span>
									<span>创建人：${internalItem.createdBy}</span>
								</span>
						  </td>
						  <td></td>
						</tr>`;
			}
          })
          .join("")}
			  </tbody>
			</table>
		  </div>`,
      )
      .join("")}
	</body>
	</html>
	`;
};
