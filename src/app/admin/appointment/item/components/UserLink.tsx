"use client"
import { $Enums } from "@prisma/client";
import { UserRound } from "lucide-react";
import Link from "next/link";
import useRole from "~/hooks/use-role";
import { cn } from "~/lib/utils";

interface UserLinkProps {
    id: string;
    name: string | null;
    className?: string;
    showIcon?: boolean;
}

export default function UserLink({ id, name, className, showIcon = false }: UserLinkProps) {
    const { rolesInclude } = useRole()
    const displayName = name ?? "已失效用户";
    const btnClass = name ? "text-indigo-800" : "text-gray-300";
    if (rolesInclude([$Enums.UserGroup.SUPERADMIN, $Enums.UserGroup.SYSADMIN])) {
        if (name) {
            return (
                <Link target="_blank" href={`/admin/user/list?keyword=${id}`}>
                    <span className={cn("flex items-center text-sm text-blue-500 underline", btnClass, className)}>
                        {showIcon && <UserRound className="w-4 h-4" />}
                        {displayName}
                    </span>
                </Link>
            );
        }
    }
    return <span className={cn("text-sm ", btnClass, className)}>{displayName}</span>;
}
