"use client"
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from "~/components/ui/command"
import { Check, LoaderCircle, TextSearch, X } from "lucide-react"
import React from "react"
import { Popover, PopoverContent, PopoverTrigger } from "~/components/ui/popover"
import { cn } from "~/lib/utils"
import { api } from "~/trpc/react"
import { usePageNavigation } from "~/hooks/use-pagenavigation"


export default function ProjectHeader() {
    const {
        getSearchParam,
        updatePageParam
    } = usePageNavigation();
    const locationId = getSearchParam("locationId");
    const projectId = getSearchParam("projectId");
    const { data, isLoading } = api.appointment.getAllProjects.useQuery({
        locationId: Number(locationId),
        pageSize: 1000
    })
    const [open, setOpen] = React.useState(false)
    const currentProject = data?.data.find((project) => project.id === Number(projectId));
    if (isLoading) {
        return (
            <LoaderCircle className="w-4 h-4 animate-spin" />
        )
    }
    return (
        <div className="flex flex-row gap-1 items-center">
            {
                currentProject ? (
                    <span className="flex flex-row gap-1 items-center">
                        {currentProject.name}
                        <span className="cursor-pointer" onClick={() => {
                            updatePageParam({
                                projectId: "",
                                sortBy: "",
                                sortOrder: ""
                            })
                        }}>
                            <X className="w-4 h-4" />
                        </span>
                    </span>
                ) : (
                    <>
                        <span>预约项目名称</span>
                    </>
                )
            }
            <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                    <TextSearch className="w-4 h-4 cursor-pointer" />
                </PopoverTrigger>
                <PopoverContent className="w-[200px] p-0">
                    <Command>
                        <CommandInput
                            placeholder="搜索项目"
                            className="h-9"
                        />
                        <CommandList>
                            {isLoading ? (
                                <CommandEmpty>Loading...</CommandEmpty>
                            ) : data?.data.length === 0 ? (
                                <CommandEmpty>没有找到项目</CommandEmpty>
                            ) : (
                                <CommandGroup>
                                    {data?.data.map((project) => (
                                        <CommandItem
                                            key={project.id}
                                            value={project.name}
                                            onSelect={(currentValue) => {
                                                setOpen(false)
                                                updatePageParam({
                                                    projectId: project.id.toString(),
                                                    sortBy: "",
                                                    sortOrder: ""
                                                })
                                            }}
                                        >
                                            {project.name}
                                            <Check
                                                className={cn(
                                                    "ml-auto",
                                                    projectId === project.id.toString() ? "opacity-100" : "opacity-0"
                                                )}
                                            />
                                        </CommandItem>
                                    ))}
                                </CommandGroup>
                            )}
                        </CommandList>
                    </Command>
                </PopoverContent>
            </Popover>
        </div>
    )
}
