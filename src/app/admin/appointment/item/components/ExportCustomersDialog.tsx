"use client";
import React from "react";
import { Dialog, DialogTrigger,DialogHeader , DialogContent, DialogTitle, DialogDescription } from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { $Enums } from "@prisma/client";
import ExportCustomers from "./ExportCustomers";


interface ExportCustomersDialogProps {
    locationId: string;
    status: $Enums.AppointmentStatus;
    locationName: string;
}

export default function ExportCustomersDialog({ locationId, status, locationName }: ExportCustomersDialogProps) {
    const [open, setOpen] = React.useState(false);
    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button variant="outline">导出外部预约人名单</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>导出外部预约名单</DialogTitle>
                </DialogHeader>
          
                <ExportCustomers
                    {...{
                        locationId,
                        status,
                        locationName,
                        onExportFinished: () => setOpen(false)
                    }}
                />
           
            </DialogContent>

        </Dialog>
    );
}