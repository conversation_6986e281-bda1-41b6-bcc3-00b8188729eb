"use client"
import { api } from "~/trpc/react";

import useExcel from "~/hooks/useExcel";
import { RouterOutputs } from "~/trpc/react";
import React from "react";
import { generateAppointmentCsv } from "./template";
import { Progress } from "~/components/ui/progress";
import { $Enums } from "@prisma/client";

interface ExportDialogProps {
  status: $Enums.AppointmentStatus;
  locationId: string;
  locationName: string;
  projectId: number | undefined;
  sortBy: string | undefined;
  sortOrder: string | undefined;
  onExportFinished: () => void;
}


const statusLabelMap: Record<string, string> = {
  [$Enums.AppointmentStatus.CONFIRMED]: "_已确认",
  [$Enums.AppointmentStatus.CHECKED]: "_已检查",
  default: "_已取消",
};


const sortLabelMap: Record<string, Record<string, string>> = {
  createdAt: {
    asc: "_按创建时间升序",
    desc: "_按创建时间降序",
  },
  startTime: {
    asc: "_按预约开始时间升序",
    desc: "_按预约开始时间降序",
  },
};

export default function ExportAppointments({ status, locationId, locationName, projectId, sortBy, sortOrder, onExportFinished }: ExportDialogProps) {
  const { generateCSVTemplate } = useExcel();
  const [progress, setProgress] = React.useState(0);
  const [page, setPage] = React.useState(1);
  const [tempData, setTempData] = React.useState<RouterOutputs["appointment"]["getAllAppointments"]["data"][number][]>([]);

  const { data, isLoading, isRefetching, refetch } = api.appointment.getAllAppointments.useQuery(
    {
      locationId: Number(locationId),
      page,
      pageSize: 10,
      status: status,
      projectId: projectId,
      sortBy: sortBy,
      sortOrder: sortOrder,
    },
    {
      enabled: !!locationId && !!status, // only fetch if params exist
      retry: false, // optional: don't retry on error
      refetchOnWindowFocus: false, // optional: avoid unwanted refetches
    }
  );

  React.useEffect(() => {
    if (data) {
      setTempData((prev) => [...prev, ...data.data]);
      setProgress(Math.floor((data.page / data.totalPages) * 100));
      if (data.totalPages > 0 && page < data.totalPages) {
        setPage(data.page + 1);
      }
    }
  }, [data]);

  React.useEffect(() => {
    if (page > 1) {
      refetch();
    }
  }, [page]);

  // when fetch finished, generate csv
  React.useEffect(() => {
    if (progress === 100) {
      let label = `${locationName}`;
      if (projectId) {
        const projectName = tempData[0]?.appointmentProject.name ?? "";
        label += `_${projectName}`;
      }
      label += statusLabelMap[status] ?? statusLabelMap.default;
      if (sortBy && sortOrder && sortLabelMap[sortBy]?.[sortOrder]) {
        label += sortLabelMap[sortBy][sortOrder];
      }
      label += "_预约数据";
      const csv = generateAppointmentCsv(label, tempData);
      generateCSVTemplate(csv, label);
      onExportFinished();
    }
  }, [progress]);
  return (
    <div>
      <Progress value={progress} className="w-full" />
    </div>
  );
}
