import { $Enums } from "@prisma/client";
import React from "react";
import Spinner from "~/app/(app)/appointment/_components/spinner";
import { Progress } from "~/components/ui/progress";
import useExcel from "~/hooks/useExcel";
import { api } from "~/trpc/react";
import { generateCustomersCsv } from "./template";
import { labels } from "./labels";

interface ExportCustomersProps {
    locationId: string;
    status: $Enums.AppointmentStatus;
    locationName: string;
    onExportFinished: () => void;
}

export default function ExportCustomers({ locationId, status, locationName, onExportFinished }: ExportCustomersProps) {
    const { generateCSVTemplate } = useExcel();
    const { data, isLoading, isRefetching, refetch } = api.appointment.getAllCustomers.useQuery({
        status: status,
        locationId: Number(locationId),
    })
    React.useEffect(() => {
        if (data && data.length > 0) {
            const label = `${locationName}_预约人名单_${labels[status]}`;
            const csv = generateCustomersCsv(label, data ?? []);
            generateCSVTemplate(csv, label);
            onExportFinished();
        }
    }, [data]);
    return (
        <div className="flex flex-col items-center justify-center gap-2">
        <Spinner className="text-primary" />
        <span className="text-sm text-gray-500">正在导出预约人名单...</span>
      </div>
    );
}