import { Progress } from "~/components/ui/progress";
import React from "react";
import { template, internalTemplate } from "./template";
import { api } from "~/trpc/react";
import { AppointmentCustomerType } from "./Customer";



interface ImportModalProps {
    id: number;
    data: string[][];
    type: AppointmentCustomerType;
}

export function ImportModal({ id, data, type }: ImportModalProps) {
    const utils = api.useUtils();
    const [progress, setProgress] = React.useState(0);
    const [processedCount, setProcessedCount] = React.useState(0);
    const [successCount, setSuccessCount] = React.useState(0);
    const [error, setError] = React.useState<string[]>([]);
    const processedRef = React.useRef(false);

    const createCustomer = api.appointment.addCustomer.useMutation();
    const createInternalCustomer = api.appointment.addInternalCustomer.useMutation();

    React.useEffect(() => {
        if (processedRef.current) return;
        processedRef.current = true;

        const validateCustomers = () => {
            const requiredColumns = type === AppointmentCustomerType.INTERNAL ? internalTemplate.Sheet1.data[0] : template.Sheet1.data[0];
 
            let existError = false;

            if (type === AppointmentCustomerType.EXTERNAL) {
                for (const [index, row] of data.entries()) {
                    if (row.length !== requiredColumns?.length || row.some((field) => field === '')) {
                        setError(errors => [...errors, `第 ${index + 1} 行数据存在缺失`]);
                        existError = true;
                        break;
                    }
                    // check phone number with 11 digits
                    const phone = row[2] ?? "";
                    if (!/^\d{11}$/.test(phone)) {
                        setError(errors => [...errors, `第 ${index + 1} 行手机号格式错误: ${phone}`]);
                        existError = true;
                        break;
                    }
                }
            } else if (type === AppointmentCustomerType.INTERNAL) {
                for (const [index, row] of data.entries()) {
                    if (row.length !== requiredColumns?.length || row.some((field) => field === '')) {
                        setError(errors => [...errors, `第 ${index + 1} 行数据存在缺失`]);
                        existError = true;
                        break;
                    }
                }
            }
            return !existError;
        };

        const processCustomers = async () => {
            if (data.length === 0 || !validateCustomers()) {
                setProcessedCount(data.length);
                setProgress(100);
                return;
            };
            let tempCount = 0;
            if (type === AppointmentCustomerType.EXTERNAL) {
                for (const [index, row] of data.entries()) {
                    try {
                        const customerData = {
                            id: id,
                            name: String(row[0]!),
                            hospital: String(row[1]!),
                            contactInfo: String(row[2]!),
                        };
                        const res = await createCustomer.mutateAsync(customerData);
                        if (res.status === "error") {
                            setError(error => [...error, res.message ?? ""])
                        } else {
                            tempCount += 1
                            setSuccessCount(count => count + 1);
                        }
                    } catch (err) {
                        setError(errors => [...errors, `导入第 ${index + 1} 行失败: 系统错误`]);
                    } finally {
                        setProcessedCount(count => index + 1);
                        setProgress(_ => 100 * (index + 1) / data.length);
                    }
                }

            } else if (type === AppointmentCustomerType.INTERNAL) {
                for (const [index, row] of data.entries()) {
                    try {
                        const customerData = {
                            id: id,
                            name: String(row[0]!),
                            department: String(row[1]!),
                        };
                        const res = await createInternalCustomer.mutateAsync(customerData);
                        if (res.status === "error") {
                            setError(error => [...error, res.message ?? ""])
                        } else {
                            tempCount += 1
                            setSuccessCount(count => count + 1);
                        }
                    } catch (err) {
                        setError(errors => [...errors, `导入第 ${index + 1} 行失败: 系统错误`]);
                    } finally {
                        setProcessedCount(count => index + 1);
                        setProgress(_ => 100 * (index + 1) / data.length);
                    }
                }
            }
            if (tempCount > 0) {
                // console.log("invalidate", id);
                await utils.invalidate(undefined, {
                    queryKey: [
                      ["appointment.getAllAppointments"],
                      ["appointment.getAppointmentCustomers" , {id: id}],
                    ],
                    refetchType: 'active',
                  });
            }
        };

        void processCustomers();


    }, [createCustomer, data, id, type, utils]);

    return (
        <>
            <div className='py-10'>
                <Progress value={progress} className="w-full" />
            </div>
            <div className='p-4 flex flex-col items-center w-full'>
                <div className='text-sm text-gray-500 text-center'>
                    {
                        processedCount === data.length ? (
                            <p>成功导入 {successCount} 个预约人信息</p>
                        ) : (
                            <>
                                <p>正在处理 {processedCount}/{data.length} • 在导入完成前，请勿操作窗口</p>
                            </>
                        )
                    }
                    {error.length > 0 && error.map((item, index) => (
                        <p key={index} className='text-sm text-red-500 text-center '>{item}</p>
                    ))}
                </div>
            </div>
        </>
    );
}