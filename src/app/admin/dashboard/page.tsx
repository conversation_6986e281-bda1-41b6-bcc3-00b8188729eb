"use client";

import { get } from "lodash";
import React from "react";
import { Bar, BarChart, CartesianGrid, XAxis, Line, LineChart, ResponsiveContainer } from "recharts"
import { format, subDays, startOfMonth, endOfMonth } from "date-fns"
import type { DateRange } from "react-day-picker"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "~/components/ui/chart"
import { Select, SelectItem, SelectContent, SelectGroup, SelectLabel, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Skeleton } from "~/components/ui/skeleton"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "~/components/ui/tabs"
import { DateRangePicker } from "~/components/ui/date-range-picker"
import { api } from "~/trpc/react";

const quarterLabels: Record<number, string> = {
  1: "Q1 (1-3月)",
  2: "Q2 (4-6月)",
  3: "Q3 (7-9月)",
  4: "Q4 (10-12月)",
}

// fill to the  the bar chart 
const colorsClassNames = [
  "fill-blue-700",
  "fill-red-700",
  "fill-green-700",
  "fill-yellow-700",
  "fill-purple-700",
  "fill-orange-700",
  "fill-pink-700",
  "fill-gray-700",
  "fill-indigo-700",
  "fill-teal-700",
  "fill-lime-700",
  "fill-fuchsia-700",
  "fill-violet-700",
  "fill-cyan-700",
  "fill-emerald-700",
  "fill-sky-700",
  "fill-rose-700"
]

export default function Dashboard() {
  const [year, setYear] = React.useState(new Date().getFullYear());
  const [viewMode, setViewMode] = React.useState<"yearly" | "dateRange">("yearly");
  
  // Date range state - default to current month
  const [dateRange, setDateRange] = React.useState<DateRange | undefined>(() => {
    const today = new Date();
    return {
      from: startOfMonth(today),
      to: endOfMonth(today)
    };
  });
  
  const [groupBy, setGroupBy] = React.useState<"day" | "week" | "month">("day");

  const { data: usersNum, isLoading: isUsersNumLoading } =
    api.user.countUsers.useQuery();
  const { data: appointmentInfo, isLoading: isAppointmentInfoLoading } =
    api.appointment.getInfo.useQuery();

  const {data: allLocations, isLoading: isAllLocationsLoading} = api.location.getLocaltions.useQuery();

  const { data: dataGroupByQuarter, isLoading: isDataGroupByQuarterLoading } =
    api.appointment.dataGroupByQuarter.useQuery({ year }, {
      enabled: viewMode === "yearly" && !!year,
  });

  const { data: dataGroupByDateRange, isLoading: isDataGroupByDateRangeLoading } =
    api.appointment.dataGroupByDateRange.useQuery(
      {
        startDate: dateRange?.from?.toISOString() ?? "",
        endDate: dateRange?.to?.toISOString() ?? "",
        groupBy,
      },
      {
        enabled: viewMode === "dateRange" && !!dateRange?.from && !!dateRange?.to,
      }
    );

  const allLocationNames = React.useMemo(() => {
    if (isAllLocationsLoading) {
      return [];
    }
    return allLocations?.data?.map((location) => location.name) || [];
  }, [allLocations, isAllLocationsLoading]);

  // Process quarterly data (existing logic)
  const [chartConfig , chartData, sumData] = React.useMemo(() => {
    if (viewMode !== "yearly" || isDataGroupByQuarterLoading) {
      return [{} as ChartConfig, [] , {} as Record<string, number>];
    }

    const sumData: Record<string, number>  = allLocationNames?.reduce((acc, name) => {
      acc[name] = 0;
      return acc;
    }, {} as Record<string, number>) ?? {};
    const chartConfig = (allLocationNames?.reduce((acc, name) => {
      acc[name] = {
        label: name
      };
      return acc;
    }, {} as Record<string, { label: string }>) || {}) satisfies ChartConfig;
    
    const zeroData: Record<string, number> = {};
    allLocationNames?.forEach((name) => {
      zeroData[name] = 0;
    });

    const quarterIndices = ['1', '2', '3', '4'] as const;
    const briefData: Record<string, Record<string, number>> = {};
    
    quarterIndices.forEach((quarter) => {
      briefData[quarter] = { ...zeroData };
    });
     
    dataGroupByQuarter?.forEach((item) => {
      if (briefData[item.quarter]) {
        briefData[item.quarter]![item.locationName] = item.appointmentCount;
        sumData[item.locationName]! += item.appointmentCount;
      }
    });
    const chartData = quarterIndices.map((quarter) => ({
      quarter: `${quarterLabels[quarter]}`,
      ...briefData[quarter]
    }));
    return [chartConfig, chartData, sumData];
    
  }, [ allLocationNames, dataGroupByQuarter , isDataGroupByQuarterLoading, viewMode]);

  // Process date range data (new logic)
  const [dateRangeChartConfig, dateRangeChartData, dateRangeSummary] = React.useMemo(() => {
    if (viewMode !== "dateRange" || isDataGroupByDateRangeLoading || !dataGroupByDateRange) {
      return [{} as ChartConfig, [], {} as Record<string, { totalAppointments: number; totalCustomers: number; completedAppointments: number }>];
    }

    const chartConfig = (allLocationNames?.reduce((acc, name) => {
      acc[name] = {
        label: name
      };
      return acc;
    }, {} as Record<string, { label: string }>) || {}) satisfies ChartConfig;

    // Group data by date and location
    const groupedData: Record<string, Record<string, number>> = {};
    
    dataGroupByDateRange.data.forEach((item) => {
      if (!groupedData[item.dateGroup]) {
        groupedData[item.dateGroup] = {};
        allLocationNames?.forEach((name) => {
          groupedData[item.dateGroup]![name] = 0;
        });
      }
      groupedData[item.dateGroup]![item.locationName] = 
        (groupedData[item.dateGroup]![item.locationName] || 0) + item.appointmentCount;
    });

    // Convert to chart data format
    const chartData = Object.entries(groupedData)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([dateGroup, locationData]) => ({
        dateGroup: format(new Date(dateGroup), groupBy === "month" ? "yyyy年MM月" : "MM月dd日"),
        ...locationData,
      }));

    // Calculate summary
    const summary = dataGroupByDateRange.summary.reduce((acc, item) => {
      acc[item.locationName] = {
        totalAppointments: item.totalAppointments,
        totalCustomers: item.totalCustomers,
        completedAppointments: item.completedAppointments,
      };
      return acc;
    }, {} as Record<string, { totalAppointments: number; totalCustomers: number; completedAppointments: number }>);

    return [chartConfig, chartData, summary];
  }, [dataGroupByDateRange, isDataGroupByDateRangeLoading, allLocationNames, viewMode, groupBy]);

  if (isUsersNumLoading || isAppointmentInfoLoading) {
    return (
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="grid auto-rows-min gap-4 md:grid-cols-4">
          <Skeleton className="aspect-video rounded-xl bg-muted p-10" />
          <Skeleton className="aspect-video rounded-xl bg-muted p-10" />
          <Skeleton className="aspect-video rounded-xl bg-muted p-10" />
          <Skeleton className="aspect-video rounded-xl bg-muted p-10" />
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        {/* Summary Cards */}
        <div className="grid auto-rows-min gap-4 grid-cols-2 md:grid-cols-4">
          <Card className="border-none  bg-zinc-100 text-indigo-900">
            <CardHeader>
              <CardTitle>用户数量</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-4xl font-bold">{usersNum}</div>
            </CardContent>
          </Card>

          <Card className="border-none bg-zinc-100 text-indigo-900">
            <CardHeader>
              <CardTitle>客户数量</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-4xl font-bold"> {get(appointmentInfo, [0])}</div>
            </CardContent>
          </Card>

          <Card className="border-none bg-zinc-100 text-indigo-900">
            <CardHeader>
              <CardTitle>总预约数量</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-4xl font-bold">{get(appointmentInfo, [1])}</div>
            </CardContent>
          </Card>

          <Card className="border-none bg-zinc-100 text-indigo-900">
            <CardHeader>
              <CardTitle>已完成预约</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-4xl font-bold">{get(appointmentInfo, [2])}</div>
            </CardContent>
          </Card>
        </div>

        {/* Analytics Section */}
        <div className="w-full md:min-h-min">
          <Card className="border-none bg-zinc-100 text-indigo-900">
            <CardHeader>
              <CardTitle>
                <div className="flex justify-between items-center">
                  <span>预约统计</span>
                  <div className="flex items-center gap-4">
                    <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as "yearly" | "dateRange")}>
                      <TabsList>
                        <TabsTrigger value="yearly">年度视图</TabsTrigger>
                        <TabsTrigger value="dateRange">日期范围</TabsTrigger>
                      </TabsList>
                    </Tabs>
                    
                    {viewMode === "yearly" && (
                      <Select defaultValue={year.toString()} onValueChange={(value) => setYear(parseInt(value))}>
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="选择年份" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>年份</SelectLabel>
                            {Array.from({ length: new Date().getFullYear() - 2024 }, (_, i) => (
                              <SelectItem key={i} value={`${2025 + i}`}>{2025 + i}</SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                    
                    {viewMode === "dateRange" && (
                      <div className="flex items-center gap-2">
                        <DateRangePicker
                          value={dateRange}
                          onChange={setDateRange}
                          placeholder="选择日期范围"
                        />
                        <Select value={groupBy} onValueChange={(value) => setGroupBy(value as "day" | "week" | "month")}>
                          <SelectTrigger className="w-[120px]">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="day">按天</SelectItem>
                            <SelectItem value="week">按周</SelectItem>
                            <SelectItem value="month">按月</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>
                </div>
              </CardTitle>
              <CardDescription className="text-gray-700 p-4">
                <div className="flex flex-row gap-2">
                  {viewMode === "yearly" && (
                    <>
                      <span className="font-bold text-sm">{year} 年：</span>
                      {allLocationNames?.map((name) => (
                        <span key={name} className="text-gray-500 text-sm">
                          {name}：<span className="font-bold">{get(sumData, [name], "0")}</span>
                        </span>
                      ))}
                    </>
                  )}
                  {viewMode === "dateRange" && dateRange?.from && dateRange?.to && (
                    <>
                      <span className="font-bold text-sm">
                        {format(dateRange.from, "yyyy年MM月dd日")} - {format(dateRange.to, "yyyy年MM月dd日")}：
                      </span>
                      {allLocationNames?.map((name) => {
                        const locationSummary = dateRangeSummary[name];
                        return (
                          <span key={name} className="text-gray-500 text-sm">
                            {name}：<span className="font-bold">{locationSummary?.totalAppointments || 0}</span>
                          </span>
                        );
                      })}
                    </>
                  )}
                </div>
              </CardDescription>
            </CardHeader>
            <CardContent>
              {viewMode === "yearly" && (
                <ChartContainer config={chartConfig} className="h-64 w-full">
                  <BarChart accessibilityLayer data={chartData} barCategoryGap="25%">
                    <CartesianGrid vertical={false} />
                    <XAxis
                      dataKey="quarter"
                      tickLine={false}
                      tickMargin={10}
                      axisLine={false}
                      tickFormatter={(value) => value}
                    />
                    <ChartTooltip
                      cursor={false}
                      content={<ChartTooltipContent indicator="dashed" />}
                    />
                    {allLocationNames?.map((name, index) => (
                      <Bar key={name} dataKey={name} className={colorsClassNames[index % colorsClassNames.length]} radius={0} />
                    ))}
                  </BarChart>
                </ChartContainer>
              )}
              
              {viewMode === "dateRange" && (
                <ChartContainer config={dateRangeChartConfig} className="h-64 w-full">
                  {groupBy === "day" ? (
                    <LineChart accessibilityLayer data={dateRangeChartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="dateGroup"
                        tickLine={false}
                        tickMargin={10}
                        axisLine={false}
                        angle={-45}
                        textAnchor="end"
                        height={60}
                      />
                      <ChartTooltip
                        content={<ChartTooltipContent indicator="dashed" />}
                      />
                      {allLocationNames?.map((name, index) => (
                        <Line 
                          key={name} 
                          type="monotone" 
                          dataKey={name} 
                          stroke={`hsl(${(index * 137.5) % 360}, 70%, 50%)`}
                          strokeWidth={2}
                          dot={{ r: 4 }}
                        />
                      ))}
                    </LineChart>
                  ) : (
                    <BarChart accessibilityLayer data={dateRangeChartData} barCategoryGap="25%">
                      <CartesianGrid vertical={false} />
                      <XAxis
                        dataKey="dateGroup"
                        tickLine={false}
                        tickMargin={10}
                        axisLine={false}
                        angle={-45}
                        textAnchor="end"
                        height={60}
                      />
                      <ChartTooltip
                        cursor={false}
                        content={<ChartTooltipContent indicator="dashed" />}
                      />
                      {allLocationNames?.map((name, index) => (
                        <Bar key={name} dataKey={name} className={colorsClassNames[index % colorsClassNames.length]} radius={0} />
                      ))}
                    </BarChart>
                  )}
                </ChartContainer>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}
