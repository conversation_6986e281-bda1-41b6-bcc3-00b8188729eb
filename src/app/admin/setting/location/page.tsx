"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { But<PERSON> } from "~/components/ui/button";
import useRightPanelStore from "~/hooks/useRightPanelStore";
import { api } from "~/trpc/react";
import { LocaltionForm } from "./components/LocationForm";
import { useToast } from "~/hooks/use-toast";
import { useConfirmationStore } from "~/hooks/useConfirmationStore";
import { ListSkeleton } from "~/components/common/Skeletons";
import { useLoading } from "~/hooks/useLoadingStore";
import { DataTable } from "~/components/common/DataTable";
import StatusIcon from "~/components/common/StatusIcon";
import TableOperation from "~/components/common/TableOperation";
import { produce } from "immer";
import { CheckCircleIcon, Edit, Trash, XCircleIcon } from "lucide-react";
import { format } from "date-fns";
import { settingNavs } from "~/constants/navs";
import AsideNav from "~/components/admin/layout/aside";
import type { locationSchema } from "~/schemas/location";
import type { z } from "zod";
import type { RouterOutputs } from "~/server/api/root";
import WithRole from "~/components/common/with-role";
import { $Enums } from "@prisma/client";
import useRole from "~/hooks/use-role";
import { className1, className2 } from "../../appointment/project/utils/class-util";
import { Badge } from "~/components/ui/badge";
import { autoConfirmPolicyOptions } from "./components/LocationForm";

const columns: ColumnDef<RouterOutputs["location"]["getLocaltions"]["data"][number]>[] = [
  {
    accessorKey: "name",
    header: "地点",
    size: 160,
    cell: ({ row }) => (
      <div className="max-w-40">
         <span className="max-w-full truncate">{row.original.name}</span>
         {
          row.original.description && (
            <p className="text-xs text-gray-500">{row.original.description}</p>
          )
         }
      </div>
    ),
  },
  {
    accessorKey: "adminEmails",
    header: "管理员邮箱",
    size: 120,
    cell: ({ row }) => (
      <div className="flex flex-wrap gap-2">
        {
          row.original.adminEmails.map((email) => {
            return (
              <Badge className="bg-purple-400 hover:bg-purple-500 text-white" key={email}>{email}</Badge>
            )
          })
        }
      </div>
    ),
  },
  {
    accessorKey: "autoConfirmPolicy",
    header: "审核策略",
    size: 200,
    cell: ({ row }) => (
      <div className="text-sm">{autoConfirmPolicyOptions[row.original.autoConfirmPolicy]}</div>
    ),
  },
  {
    accessorKey: "notifyPolicy",
    header: "通知策略",
    size: 200,
    // 根据notifyPolicy的值，返回对应的通知策略
    cell: ({ row }) => {
      const n = {
        create: (row.original.notifyPolicy & 1) !== 0,
        confirm: (row.original.notifyPolicy & 2) !== 0,
        cancel: (row.original.notifyPolicy & 4) !== 0,
      }
      return (
        <div className="flex flex-col space-y-4">
          <span className="flex items-center gap-2 text-sm">{n.create ? <CheckCircleIcon className="w-4 h-4 text-green-500" /> : <XCircleIcon className="w-4 h-4 text-rose-500" />}预约创建审批提醒</span>
          <span className="flex items-center gap-2 text-sm">{n.confirm ? <CheckCircleIcon className="w-4 h-4 text-green-500" /> : <XCircleIcon className="w-4 h-4 text-rose-500" />}预约确认提醒</span>
          <span className="flex items-center gap-2 text-sm">{n.cancel ? <CheckCircleIcon className="w-4 h-4 text-green-500" /> : <XCircleIcon className="w-4 h-4 text-rose-500" />}预约取消提醒</span>
        </div>
      ) 
    },
  },
  {
    accessorKey: "visiable",
    header: "可见",
    size: 80,
    cell: ({ row }) => (
      <div className="w-[30px]">
        <StatusIcon check={row.getValue("visiable")} />
      </div>
    ),
  },
  {
    accessorKey: "createdAt",
    header: "创建时间",
    size: 120,
    cell: ({ row }) => (
      <div>{format(new Date(row.original.createdAt), "yyyy-MM-dd HH:mm")}</div>
    ),
  },
  {
    accessorKey: "updatedAt",
    header: "更新时间",
    size: 120,
    cell: ({ row }) => (
      <div>{format(new Date(row.original.updatedAt), "yyyy-MM-dd HH:mm")}</div>
    ),
  },
];

export default function Page(): JSX.Element {
  const utils = api.useUtils();
  const { rolesInclude } = useRole();
  const { toast } = useToast();
  const [showPanel] = useRightPanelStore((state) => [
    state.showPanel,
    state.hidePanel,
  ]);
  const showConfirmation = useConfirmationStore(
    (state) => state.showConfirmation,
  );
  const { startLoading, stopLoading } = useLoading("global");
  const { data,  error, isLoading, isRefetching } =
    api.location.getLocaltions.useQuery();
  const saveLocationMutation = api.location.saveLocation.useMutation();
  const deleteLocationMutation = api.location.deleteLocation.useMutation();
  const handleSubmit = async (formData: z.infer<typeof locationSchema>) => {
    const res = await saveLocationMutation.mutateAsync(formData);
    if (res.status === "success"){
      toast({
        title: "成功",
        description: "地点已成功保存",
      });
      await utils.invalidate(undefined, {
        queryKey: ["location.getLocaltions"],
        refetchType: 'active',
      });
    } else {
      toast({
        title: "错误",
        description: "保存地点时发生错误",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (id: number) => {
    showConfirmation(
      "确定删除这个地点吗 ? 该地点以及该地点下包含的所有相关内容都将被删除。",
      async () => {
        startLoading();
        const res =  await deleteLocationMutation.mutateAsync({ id });
        if( res.status === "success"){
          toast({
            title: "成功",
            description: "已经成功删除该地点",
          });
          await utils.invalidate(undefined, {
            queryKey: ["location.getLocaltions"],
            refetchType: 'active',
          });
        } else {
          toast({
            title: "错误",
            description: "该地点无法被删除，请检查该地点下是否存在可预约项目",
            variant: "destructive",
          });
        }
        stopLoading();
      },
    );
  };

  const tableColumns = React.useMemo<ColumnDef<any>[]>(() => {
    return produce(columns, (draft: ColumnDef<any>[]) => {
      if (rolesInclude([$Enums.UserGroup.SUPERADMIN, $Enums.UserGroup.SYSADMIN])) {
        draft.push({
          accessorKey: "actions-column",
          header: "操作",
          size: 60,
          cell: ({ row }) => {
            const operations = [
              {
                label: "编辑地点",
                onClick: () => {
                  showPanel(
                    "编辑",
                    <LocaltionForm
                      initialData={{
                        id: row.original.id,
                        name: row.original.name,
                        autoConfirmPolicy: row.original.autoConfirmPolicy,
                        notifyPolicy: row.original.notifyPolicy,
                        adminEmails: row.original.adminEmails,
                        description: row.original.description,
                        virtual: row.original.virtual,
                        visiable: row.original.visiable,
                      }}
                      onSubmit={handleSubmit}
                    />,
                  );
                },
                icon: Edit,
                className: className1,
              },

            ];

            if (rolesInclude([$Enums.UserGroup.SYSADMIN])){
              operations.push(
                {
                  label: "删除地点",
                  onClick: () => handleDelete(Number(row.original.id)),
                  icon: Trash,
                  className: className2,
                }
              )
            }

            return (
              <div className="w-[30px]">
                <TableOperation operations={operations} label="操作" />
              </div>
            );
          },
        });
      }
    });
  }, []);

  if (isLoading) {
    return <ListSkeleton />;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  return (
    <>
      <AsideNav activeId={32} title="设置" navs={settingNavs}>
        <WithRole roles={[ $Enums.UserGroup.SYSADMIN]}>
          <Button
            onClick={() => {
              showPanel("添加地点", <LocaltionForm onSubmit={handleSubmit} />);
            }}
            className="rounded bg-blue-500 px-4 py-2 font-bold text-white hover:bg-blue-700"
          >
            添加地点
          </Button>
        </WithRole>

      </AsideNav>

      <div className="bg-neutral-400/20 p-8">
        <DataTable
          isRefetching={isRefetching}
          columns={tableColumns}
          data={data?.data ?? []}
        />
      </div>
    </>
  );
}
