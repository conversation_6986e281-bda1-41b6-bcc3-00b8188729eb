import React, { useState } from "react";
import { z } from "zod";
import { Input } from "~/components/ui/input";
import { Switch } from "~/components/ui/switch";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "~/components/ui/form";
import { useForm } from "react-hook-form";

import { Textarea } from "~/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { locationSchema, locationEmailSchema } from "~/schemas/location";
import useFormSubmitLoading from "~/hooks/useFormSubmitLoading";
import { Button } from "~/components/ui/button";
import { Trash2 } from "lucide-react";
import { Checkbox } from "~/components/ui/checkbox";
import { $Enums } from "@prisma/client";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";



export const autoConfirmPolicyOptions: Record<$Enums.AutoConfirmPolicy, string> = {
  [$Enums.AutoConfirmPolicy.NEVER]: "所有预约需手动审核并确认",
  [$Enums.AutoConfirmPolicy.PERIOD_NOT_MEET_WEEKEND_AND_HOLIDAY]: "当预约包含周末及节假日时手动审核并确认",
  [$Enums.AutoConfirmPolicy.ALWAYS]: "所有预约自动审核并确认",
}



export type FormValues = z.infer<typeof locationSchema>;

type LocationFormProps = {
  initialData?: FormValues;
  onSubmit: (data: FormValues) => Promise<void>;
};

type NotifyPolicy = {
  create: boolean;
  confirm: boolean;
  cancel: boolean;
};

export function LocaltionForm({ initialData, onSubmit }: LocationFormProps) {
  const id = React.useId()
  const [notifyPolicy, setNotifyPolicy] = React.useState<NotifyPolicy>({
    create: false,
    confirm: false,
    cancel: false,
  });
  const { handleSubmit, createFormButtons } = useFormSubmitLoading<FormValues>(onSubmit);
  const FormButtons = createFormButtons();
  const form = useForm({
    resolver: zodResolver(locationSchema),
    defaultValues: initialData || {
      name: "",
      description: "",
      adminEmails: [],
      autoConfirmPolicy: $Enums.AutoConfirmPolicy.NEVER,
      notifyPolicy: 0,
      virtual: false,
      visiable: true,
    },
  });

  React.useEffect(() => {
    if (initialData) {
      form.reset(initialData);
      if (initialData.notifyPolicy) {
        setNotifyPolicy({
          create: (initialData.notifyPolicy & 1) !== 0,
          confirm: (initialData.notifyPolicy & 2) !== 0,
          cancel: (initialData.notifyPolicy & 4) !== 0,
        });
      }
    }
  }, [initialData, form]);

  React.useEffect(() => {
    // calculate notifyPolicy
    const notifyPolicyValue =
    (notifyPolicy.create ? 1 : 0) |
    (notifyPolicy.confirm ? 2 : 0) |
    (notifyPolicy.cancel ? 4 : 0);
    form.setValue("notifyPolicy", notifyPolicyValue);  
  }, [notifyPolicy, form]);

 

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 p-8 border  bg-white shadow-lg">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>地点名称</FormLabel>
              <FormControl>
                <Input placeholder="地点名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>地点描述</FormLabel>
              <FormControl>
                <Textarea placeholder="地点描述" {...field} value={field.value ?? ""} />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="adminEmails"
          render={({ field }) => (
            <FormItem>
              <FormLabel>管理员邮箱</FormLabel>
              <FormControl>
                {/* Wrapper div for input and the list of emails */}
                <div className="space-y-2">
                  <Input
                    placeholder="输入邮箱后按回车键添加"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault(); // Prevent form submission on Enter

                        const inputElement = e.currentTarget; // Get the input element
                        const newEmail = inputElement.value.trim(); // Get and trim the current value from input

                        if (newEmail === "") { // If trimmed email is empty, do nothing or clear
                          inputElement.value = ""; // Clear if user just pressed enter on whitespace
                          return;
                        }

                        const currentEmails = Array.isArray(field.value) ? field.value : [];

                        if (currentEmails.includes(newEmail)) {
                          // Optional: Notify user that email is already in the list
                          // You could use a toast notification here.
                          console.warn(`邮箱 "${newEmail}" 已经添加过了。`);
                          inputElement.value = ""; // Clear input even if duplicate to allow new entry
                          return;
                        }

                        if (!locationEmailSchema.safeParse(newEmail).success) {
                          console.warn(`邮箱 "${newEmail}" 格式不正确。`);
                          inputElement.value = ""; // Clear input even if duplicate to allow new entry
                          return;
                        }

                        // Add the new email to the array in RHF's state
                        field.onChange([...currentEmails, newEmail]);
                        inputElement.value = ""; // Clear the input field for the next email
                      }
                    }}
                  // field.onBlur might be problematic here as it's for the array field,
                  // not this specific text input. Blurring this input shouldn't necessarily
                  // trigger validation for the whole list unless desired.
                  />

                  {/* Display the list of added emails */}
                  {Array.isArray(field.value) && field.value.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {field.value.map((email, index) => (
                        <div
                          key={`${email}-${index}`} // A more resilient key if emails could temporarily be non-unique
                          className="py-2 px-4 h-8  flex items-center justify-between text-xs border rounded-full space-x-2 bg-secondary/30"
                        >
                          <span>{email}</span>
                          <Button
                            type="button" // Important: Prevents form submission
                            variant="ghost" // Subtle button style
                            size="icon"
                            className="w-4 h-4 hover:bg-destructive/20 hover:text-destructive" // Custom hover for delete
                            onClick={() => {
                              // Remove the email by filtering the array
                              const updatedEmails = field.value?.filter((_, i) => i !== index) ?? [];
                              field.onChange(updatedEmails);
                            }}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </FormControl>
              <FormMessage /> {/* This will display Zod validation errors for the 'adminEmails' array */}
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="notifyPolicy"
          render={({ field }) => (
            <FormItem>
              <FormLabel>该地点下预约邮件提醒策略</FormLabel>
              <FormControl>
                <div className="flex gap-6">
                  <div className="flex items-center gap-2">
                    <Checkbox 
                      checked={notifyPolicy.create}
                      onCheckedChange={(checked) => setNotifyPolicy({ ...notifyPolicy, create: checked === true })}
                      id={`${id}-a`} 
                    />
                    <span className="text-sm">预约创建审批提醒</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Checkbox 
                      checked={notifyPolicy.confirm}
                      onCheckedChange={(checked) => setNotifyPolicy({ ...notifyPolicy, confirm: checked === true })}
                      id={`${id}-b`} 
                    />
                    <span className="text-sm">预约确认提醒</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Checkbox 
                      checked={notifyPolicy.cancel}
                      onCheckedChange={(checked) => setNotifyPolicy({ ...notifyPolicy, cancel: checked === true })}
                      id={`${id}-c`} 
                    />
                    <span className="text-sm">预约取消提醒</span>
                  </div>
                </div>
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="autoConfirmPolicy"
          render={({ field }) => (
            <FormItem>
              <FormLabel>该地点下预约审核策略</FormLabel>
              <FormControl>
                <RadioGroup 
                  className="flex flex-col  gap-2" 
                  defaultValue={field.value}
                  onValueChange={field.onChange}
                >
                  {
                    Object.entries(autoConfirmPolicyOptions).map(([key, value]) => (
                      <div key={key} className="flex items-center gap-2">
                        <RadioGroupItem 
                          value={key} 
                          id={`${id}-${key}`} 
                        />
                        <span className="text-sm">{value}</span>
                      </div>
                    ))
                  }
                </RadioGroup>
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="visiable"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
              <div className="space-y-0.5">
                <FormLabel>显示地点</FormLabel>
                <FormDescription>
                  是否显示该地点
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
        {FormButtons}
      </form>
    </Form>
  );
}