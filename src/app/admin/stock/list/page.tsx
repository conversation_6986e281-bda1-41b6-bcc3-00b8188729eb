"use client"
import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { produce } from "immer"
import { type Location, StockItem, TransactionType } from "@prisma/client";
import { Button } from '~/components/ui/button';
import { useToast } from '~/hooks/use-toast';

import { DataTable } from '~/components/common/DataTable';
import { ItemForm, ItemFormData } from './components/ItemForm';
import useRightPanelStore from '~/hooks/useRightPanelStore';
import { api } from '~/trpc/react';
import { useLoading } from '~/hooks/useLoadingStore';
import { ListSkeleton } from '~/components/common/Skeletons';
import { cn } from '~/lib/utils';
import TableOperation from '~/components/common/TableOperation';
import { FileBox, PackageMinus, PackagePlus, PackageX, Trash } from 'lucide-react';
import StockItemTransaction from './components/StockItemTransaction';
import { Input } from '~/components/ui/input';
import { useRouter, useSearchParams } from 'next/navigation'
import debounce from 'lodash/debounce'

import { StockRouterOutputs } from '~/server/api/routers/stock';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover"
import { BasicForm, BasicFormData } from './components/BasicForm';
import { pick } from 'lodash';



const columns: ColumnDef<StockRouterOutputs["getStockItems"]["items"][number]>[] = [
  {
    accessorKey: "id",
    header: "编号",
    size: 50,
  },
  {
    accessorKey: "name",
    header: "耗材图片",
    size: 100,
    cell: ({ row }) => {
      return (
        <div className="w-10 h-10">
          {row.original.coverFile?.url && (
            <Popover>
              <PopoverTrigger asChild>
                <img
                  src={row.original.coverFile.url}
                  alt="Item Image"
                  className="w-full h-full object-cover cursor-zoom-in"
                />
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 border-2">
                <img
                  src={row.original.coverFile.url}
                  alt="Item Preview"
                  className="max-w-[300px] max-h-[300px] object-contain"
                />
              </PopoverContent>
            </Popover>
          )}
        </div>
      )
    }
  },
  {
    accessorKey: "sku",
    header: "SKU",
    size: 200,
  },
  {
    accessorKey: "description",
    header: "描述",
    size: 400,
    // cell: ({ row }) => {
    //   const description = row.original.description;
    //   return <div dangerouslySetInnerHTML={{ __html: description || '' }} />;
    // }
  },
  {
    accessorKey: "currentQuantity",
    header: "库存"
  },
]




export default function Page(): JSX.Element {
  const { toast } = useToast()
  const router = useRouter()
  const searchParams = useSearchParams()

  const [locationId, setLocationId] = React.useState<number | undefined>(
    searchParams.get('locationId') ? Number(searchParams.get('locationId')) : undefined
  )
  const { startLoading, stopLoading } = useLoading('global');
  const [showPanel, hidePanel] = useRightPanelStore((state) => [state.showPanel, state.hidePanel]);
  const [searchParam, setSearchParam] = React.useState<string>(
    searchParams.get('search') || ''
  )
  const [inputValue, setInputValue] = React.useState<string>(searchParam)

  // Create debounced function for updating URL and triggering search
  const debouncedUpdate = React.useMemo(
    () =>
      debounce((searchValue: string) => {
        const params = new URLSearchParams(searchParams)
        if (searchValue) {
          params.set('search', searchValue)
        } else {
          params.delete('search')
        }
        router.push(`?${params.toString()}`)
      }, 500),
    [searchParams]
  )

  // Update API query to include search
  const { data, refetch, error, isRefetching, isLoading } = api.stock.getStockItems.useQuery({
    page: 1,
    pageSize: 10,
    locationId: locationId,
    keyword: searchParam
  })

  // Cleanup debounce on unmount
  React.useEffect(() => {
    return () => {
      debouncedUpdate.cancel()
    }
  }, [])

  const createStockMutation = api.stock.createStockItem.useMutation({
    onSuccess: () => {
      refetch();

      toast({
        title: "Success",
        description: ` 库存项目创建成功`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: ` 库存项目创建失败`,
        variant: "destructive",
      });
    },
  });

  const updateStockInfoMutation = api.stock.updateStockItemInfo.useMutation({
    onSuccess: () => {
      refetch();
      toast({
        title: "Success",
        description: ` 库存项目信息更新成功`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: ` 库存项目信息更新失败`,
        variant: "destructive",
      });
    },
  });

  const transactionMutation = api.stock.updateStockQuantity.useMutation({
    onSuccess: () => {
      refetch();
      toast({
        title: "Success",
        description: ` 库存数量更新成功`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: ` 库存数量更新失败`,
        variant: "destructive",
      });
    },
  });


  const handleSubmit = async (formData: ItemFormData) => {
    await createStockMutation.mutateAsync(formData);
  }

  const handleUpdate = async (formData: BasicFormData) => {
    await updateStockInfoMutation.mutateAsync(formData);
  }


  const handleTransactionSubmit = async (data: { stockItemId: number; type: TransactionType; quantity: number }) => {
    await transactionMutation.mutateAsync(data);
  }


  const tableColumns = React.useMemo<typeof columns>(() => {
    return produce(columns, (draft: ColumnDef<any>[]) => {
      draft.push({
        accessorKey: "actions-column",
        header: "操作",
        size: 50,
        cell: ({ row }) => {
          const operations = [
            {
              label: "编辑",
              onClick: () => {
                showPanel("编辑", (
                  <BasicForm
                    itemImageUrl={row.original.coverFile?.url}
                    initialData={pick(row.original, ["id", "name", "description"])}
                    onSubmit={handleUpdate}
                  />)
                )
              },
              icon: FileBox ,
              className: cn(
                "text-blue-600 focus:bg-blue-50 focus:text-blue-600",
                "dark:text-blue-400 dark:focus:bg-blue-950 dark:focus:text-blue-400"
              )
            },
            {
              label: "入库",
              onClick: () => {
                showPanel("入库", (
                  <StockItemTransaction
                    initialType={TransactionType.ADDITION}
                    name={row.original.name!}
                    stockItemId={row.original.id!}
                    onSubmit={handleTransactionSubmit}
                  />
                ))

              },
              icon: PackagePlus,
              className: cn(
                "text-sky-600 focus:bg-sky-50 focus:text-sky-600",
                "dark:text-sky-400 dark:focus:bg-sky-950 dark:focus:text-sky-400"
              )
            },

            {
              label: "出库",
              onClick: () => {
                showPanel("出库", (
                  <StockItemTransaction
                    initialType={TransactionType.CONSUMPTION}
                    name={row.original.name!}
                    stockItemId={row.original.id!}
                    onSubmit={handleTransactionSubmit}
                  />
                ))

              },
              icon: PackageMinus,
              className: cn(
                "text-sky-600 focus:bg-sky-50 focus:text-sky-600",
                "dark:text-sky-400 dark:focus:bg-sky-950 dark:focus:text-sky-400"
              )
            },
            {
              label: "删除",
              onClick: () => { },
              icon: PackageX,
              className: cn(
                "text-red-600 focus:bg-red-50 focus:text-red-600",
                "dark:text-red-400 dark:focus:bg-red-950 dark:focus:text-red-400"
              )
            }
          ];

          return (
            <TableOperation operations={operations} label="库存操作" />
          );
        },
      })
    })
  }, [])

  if (isLoading) {
    return (
      <ListSkeleton />
    )
  }
  if (error) {
    return <div>Error: {error.message}</div>;
  }


  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">库存列表</h1>
        <Button onClick={() => {
          showPanel("添加物品", <ItemForm onSubmit={handleSubmit} />)
        }} className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
          添加库存项目
        </Button>
      </div>
      <hr />
      <div className="w-full flex justify-start items-center my-4">
        <span className='mr-2'>
          <div className="flex w-full max-w-sm items-center space-x-2">
            <Input
              value={inputValue}
              onChange={(e) => {
                setInputValue(e.target.value)
              }}
              placeholder="SKU或名称"
              className='bg-gray-50'
            />
            <Button
              type="submit"
              onClick={() => {
                setSearchParam(inputValue)
                debouncedUpdate(inputValue)
                refetch()
              }}
            >
              搜索
            </Button>
          </div>
        </span>

      </div>
      <div className='bg-white p-4'>
        <DataTable
          columns={tableColumns}
          data={data?.items || []}
          isRefetching={isRefetching}
        />
      </div>
    </>
  );
};